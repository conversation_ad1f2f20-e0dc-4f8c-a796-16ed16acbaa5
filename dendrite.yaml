# This is the Dendrite configuration file.
#
# The configuration is split up into sections - each Dendrite component has a
# configuration section, in addition to the "global" section which applies to
# all components.

# The version of the configuration file.
version: 2

# Global Matrix configuration. This configuration applies to all components.
global:
  # The domain name of this homeserver.
  server_name: localhost

  # The path to the signing private key file, used to sign requests and events.
  # Note that this is NOT the same private key as used for TLS! To generate a
  # signing key, use "./bin/generate-keys --private-key matrix_key.pem".
  private_key: /dendrite/matrix_key.pem

  # The paths and expiry timestamps (as a UNIX timestamp in millisecond precision)
  # to old signing keys that were formerly in use on this domain name. These
  # keys will not be used for federation request or event signing, but will be
  # provided to any other homeserver that asks when trying to verify old events.
  old_private_keys:
  #  If the old private key file is available:
  #  - private_key: old_matrix_key.pem
  #    expired_at: 1601024554498
  #  If only the public key (in base64 format) and key ID are known:
  #  - public_key: mn59Kxfdq9VziYHSBzI7+EDPDcBS2Xl7jeUdiiQcOnM=
  #    key_id: ed25519:mykeyid
  #    expired_at: 1601024554498

  # How long a remote server can cache our server signing key before requesting it
  # again. Increasing this number will reduce the number of requests made by other
  # servers for our key but increases the period that a compromised key will be
  # considered valid by other homeservers.
  key_validity_period: 168h0m0s

  # Global database connection pool, for PostgreSQL monolith deployments only. If
  # this section is populated then you can omit the "database" blocks in all other
  # sections. For monolith deployments using SQLite databases,
  # you must configure the "database" block for each component instead.
  database:
    connection_string: ****************************************************/dendrite?sslmode=disable
    max_open_conns: 90
    max_idle_conns: 5
    conn_max_lifetime: -1

  # Configuration for in-memory caches. Caches can often improve performance by
  # keeping frequently accessed items (like events, identifiers etc.) in memory
  # rather than having to read them from the database.
  cache:
    # The estimated maximum size for the global cache in bytes, or in terabytes,
    # gigabytes, megabytes or kilobytes when the appropriate 'tb', 'gb', 'mb' or
    # 'kb' suffix is specified. Note that this is not a hard limit, nor is it a
    # memory limit for the entire process. A cache that is too small may ultimately
    # provide little or no benefit.
    max_size_estimated: 1gb

    # The maximum amount of time that a cache entry can live for in memory before
    # it will be evicted and/or refreshed from the database. Lower values result in
    # easier admission of new cache entries but may also increase database load in
    # comparison to higher values, so adjust conservatively. Higher values may make
    # it harder for new items to make it into the cache, e.g. if new rooms suddenly
    # become popular.
    max_age: 1h

  # The server name to delegate server-server communications to, with optional port
  # e.g. localhost:443
  well_known_server_name: ""

  # The base URL to delegate client-server communications to e.g. https://localhost
  well_known_client_name: ""

  # The server name to delegate sliding sync communications to, with optional port.
  # Requires `well_known_client_name` to also be configured.
  well_known_sliding_sync_proxy: ""

  # Lists of domains that the server will trust as identity servers to verify third
  # party identifiers such as phone numbers and email addresses.
  trusted_third_party_id_servers:  # 可信任的服务器
    - matrix.org
    - vector.im

  # Disables federation. Dendrite will not be able to communicate with other servers
  # in the Matrix federation and the federation API will not be exposed.
  disable_federation: false

  # Configures the handling of presence events. Inbound controls whether we receive
  # presence events from other servers, outbound controls whether we send presence
  # events for our local users to other servers.
  presence:
    enable_inbound: false #  想看别人是否在线？设为
    enable_outbound: false # 想让别人看到你在线？设为  对外广播 大概是不同服务器之间 到时候看看


  # Configures phone-home statistics reporting. These statistics contain the server
  # name, number of active users and some information on your deployment config.
  # We use this information to understand how Dendrite is being used in the wild.
  report_stats:
    enabled: false  # 上报  默认关闭的 可以改地址 
    endpoint: https://panopticon.matrix.org/push

  # Server notices allows server admins to send messages to all users on the server.
  server_notices:   # 服务器通知 会对每一个用户 进行类似单聊的通知 点对点发送
    enabled: false
    # The local part, display name and avatar URL (as a mxc:// URL) for the user that
    # will send the server notices. These are visible to all users on the deployment.
    local_part: "_server"
    display_name: "Server Alerts"
    avatar_url: ""
    # The room name to be used when sending server notices. This room name will
    # appear in user clients.
    room_name: "Server Alerts"

  # Configuration for NATS JetStream
  jetstream: 
    # A list of NATS Server addresses to connect to. If none are specified, an
    # internal NATS server will be started automatically when running Dendrite in
    # monolith mode.
    addresses: 
    # - localhost:4222

    # Disable the validation of TLS certificates of NATS. This is
    # not recommended in production since it may allow NATS traffic
    # to be sent to an insecure endpoint.
    disable_tls_validation: false

    # Persistent directory to store JetStream streams in. This directory should be
    # preserved across Dendrite restarts.
    storage_path: ./

    # The prefix to use for stream names for this homeserver - really only useful
    # if you are running more than one Dendrite server on the same NATS deployment.
    topic_prefix: Dendrite

  # Configuration for Prometheus metric collection. // Prometheus 指标收集接口
  metrics:
    enabled: false
    basic_auth:
      username: metrics
      password: metrics

  # Optional DNS cache. The DNS cache may reduce the load on DNS servers if there
  # is no local caching resolver available for use.
  dns_cache: # 不考虑  
    enabled: false
    cache_size: 256
    cache_lifetime: "5m" # 5 minutes; https://pkg.go.dev/time@master#ParseDuration

# Configuration for the Appservice API.
app_service_api: #作用 桥接服务 例如  桥接服务是 Matrix 实现“万物互联”的关键工具之一 。它不仅增强了 Matrix 的兼容性，也帮助用户摆脱单一平台限制，实现真正的去中心化通信。 
  # Disable the validation of TLS certificates of appservices. This is
  # not recommended in production since it may allow appservice traffic
  # to be sent to an insecure endpoint.
  disable_tls_validation: false  # https 

  # Send the access_token query parameter with appservice requests in addition
  # to the Authorization header. This can cause hs_tokens to be saved to logs,
  # so it should not be enabled unless absolutely necessary.
  legacy_auth: false
  # Use the legacy unprefixed paths for appservice requests.
  legacy_paths: false # 支持_matrix 路径 

  # Appservice configuration files to load into this homeserver.
  config_files:
  #  - /path/to/appservice_registration.yaml

# Configuration for the Client API.
client_api:
  # Prevents new users from being able to register on this homeserver, except when
  # using the registration shared secret below.
  registration_disabled: false 

  # Prevents new guest accounts from being created. Guest registration is also
  # disabled implicitly by setting 'registration_disabled' above.
  guests_disabled: true

  # If set, allows registration by anyone who knows the shared secret, regardless
  # of whether registration is otherwise disabled.
  registration_shared_secret: "UgZQgeWjJvsIBvN7S_vR9-pgi6KqY4lmyQzYu25zBdk="

  # Whether to require reCAPTCHA for registration. If you have enabled registration
  # then this is HIGHLY RECOMMENDED to reduce the risk of your homeserver being used
  # for coordinated spam attacks.
  # 验证码注册
  enable_registration_captcha: false

  # Settings for ReCAPTCHA.   # 验证码注册 配置
  recaptcha_public_key: "your_recaptcha_site_key_here"
  recaptcha_private_key: "your_recaptcha_secret_key_here"
  recaptcha_bypass_secret: ""

  # To use hcaptcha.com instead of ReCAPTCHA, set the following parameters, otherwise just keep them empty.
  # recaptcha_siteverify_api: "https://hcaptcha.com/siteverify"
  # recaptcha_api_js_url: "https://js.hcaptcha.com/1/api.js"
  # recaptcha_form_field: "h-captcha-response"
  # recaptcha_sitekey_class: "h-captcha"

  # TURN server information that this homeserver should send to clients.
  # 打洞服务 
  turn:
    turn_user_lifetime: "5m"
    turn_uris:
    #  - turn:turn.server.org?transport=udp
    #  - turn:turn.server.org?transport=tcp
    turn_shared_secret: ""
    # If your TURN server requires static credentials, then you will need to enter
    # them here instead of supplying a shared secret. Note that these credentials
    # will be visible to clients!
    # turn_username: ""
    # turn_password: ""

  # Settings for rate-limited endpoints. Rate limiting kicks in after the threshold
  # number of "slots" have been taken by requests from a specific host. Each "slot"
  # will be released after the cooloff time in milliseconds. Server administrators
  # and appservice users are exempt from rate limiting by default.
  rate_limiting:
    enabled: true
    threshold: 20
    cooloff_ms: 500
    exempt_user_ids:
    #  - "@user:domain.com"

# Configuration for the Federation API.
federation_api:
  # How many times we will try to resend a failed transaction to a specific server. The
  # backoff is 2**x seconds, so 1 = 2 seconds, 2 = 4 seconds, 3 = 8 seconds etc. Once
  # the max retries are exceeded, Dendrite will no longer try to send transactions to
  # that server until it comes back to life and connects to us again.
  send_max_retries: 16

  # Disable the validation of TLS certificates of remote federated homeservers. Do not
  # enable this option in production as it presents a security risk!
  disable_tls_validation: false

  # Disable HTTP keepalives, which also prevents connection reuse. Dendrite will typically
  # keep HTTP connections open to remote hosts for 5 minutes as they can be reused much
  # more quickly than opening new connections each time. Disabling keepalives will close
  # HTTP connections immediately after a successful request but may result in more CPU and
  # memory being used on TLS handshakes for each new connection instead.
  disable_http_keepalives: false

  # Perspective keyservers to use as a backup when direct key fetches fail. This may
  # be required to satisfy key requests for servers that are no longer online when
  # joining some rooms.
  key_perspectives:
    - server_name: matrix.org
      keys:
        - key_id: ed25519:auto
          public_key: Noi6WqcDj0QmPxCNQqgezwTlBKrfqehY1u2FyWP9uYw
        - key_id: ed25519:a_RXGa
          public_key: l8Hft5qXKn1vfHrg3p4+W8gELQVo8N13JkluMfmn2sQ

  # This option will control whether Dendrite will prefer to look up keys directly
  # or whether it should try perspective servers first, using direct fetches as a
  # last resort.
  prefer_direct_fetch: false

  # deny_networks and allow_networks are the CIDR ranges used to prevent requests
  # from accessing private IPs. If your system has specific IPs it should never
  # contact, add them here with CIDR notation.
  #
  # The deny list is checked before the allow list.
  deny_networks:
    - "127.0.0.1/8"
    - "10.0.0.0/8"
    - "**********/12"
    - "***********/16"
    - "**********/10"
    - "***********/16"
    - "::1/128"
    - "fe80::/64"
    - "fc00::/7"
  allow_networks:
    - "0.0.0.0/0" # "Everything". The deny list will help limit this.

# Configuration for the Media API.
media_api:
  # Storage path for uploaded media. May be relative or absolute.
  base_path: ./media_store

  # The maximum allowed file size (in bytes) for media uploads to this homeserver
  # (0 = unlimited). If using a reverse proxy, ensure it allows requests at least
  #this large (e.g. the client_max_body_size setting in nginx).
  max_file_size_bytes: 10485760

  # Whether to dynamically generate thumbnails if needed.
  dynamic_thumbnails: false

  # The maximum number of simultaneous thumbnail generators to run.
  max_thumbnail_generators: 10

  # A list of thumbnail sizes to be generated for media content.
  thumbnail_sizes:
    - width: 32
      height: 32
      method: crop
    - width: 96
      height: 96
      method: crop
    - width: 640
      height: 480
      method: scale

# AI service configuration - provides AI conversation functionality
ai_service:
  # Whether to enable AI service
  enabled: true
  
  # AI user's Matrix ID
  ai_user_id: "@ai.zhoujiayi:localhost"
  
  # AI user display name
  ai_user_name: "AI Assistant"
  
  # List of keywords that trigger AI replies
  triggers:
    - "@ai"
    - "ai"
    - "助手"
    - "AI"
  
  # Maximum reply length
  max_length: 1000
  
  # External AI API endpoint (optional, for integrating external AI services)
  api_endpoint: "https://dev-api-platform.ope.ai/v1/chat/completions"
  
  # API key (optional)
  api_key: "sk-jKZNkcD53rsQJdo7I0hwsnR6364wjThd5OMNXsxxCoLSqHat"
  
  # Reply delay (milliseconds), simulates real user typing time
  reply_delay: 1000
  
  # Whether to automatically join AI user to rooms
  auto_join_rooms: false

# Red packet service configuration - provides red packet functionality
redpacket:
  # Whether to enable red packet service
  enabled: true
  
  # Maximum red packet amount limit
  max_amount: 200.0
  
  # Maximum red packet count limit
  max_count: 100
  
  # Default expiration time (seconds)
  default_expires_in: 86400
  
  # Minimum red packet amount
  min_amount: 0.01
  
  # Whether to allow lucky red packets
  allow_lucky_redpacket: true
  
  # 独立的数据库配置 - 复制自全局配置
  database:
    connection_string: ****************************************************/dendrite?sslmode=disable
    max_open_conns: 90
    max_idle_conns: 5
    conn_max_lifetime: -1

# Configuration for enabling experimental MSCs on this homeserver.
mscs:
  mscs:
  #  - msc2836  # (Threading, see https://github.com/matrix-org/matrix-doc/pull/2836)

# Configuration for the Sync API.
sync_api:
  # This option controls which HTTP header to inspect to find the real remote IP
  # address of the client. This is likely required if Dendrite is running behind
  # a reverse proxy server.
  # real_ip_header: X-Real-IP

  # Configuration for the full-text search engine.
  search:
    # Whether or not search is enabled.
    enabled: false

    # The path where the search index will be created in.
    index_path: "./searchindex"

    # The language most likely to be used on the server - used when indexing, to
    # ensure the returned results match expectations. A full list of possible languages
    # can be found at https://github.com/blevesearch/bleve/tree/master/analysis/lang
    language: "en"

# Configuration for the User API.
user_api:
  # The cost when hashing passwords on registration/login. Default: 10. Min: 4, Max: 31
  # See https://pkg.go.dev/golang.org/x/crypto/bcrypt for more information.
  # Setting this lower makes registration/login consume less CPU resources at the cost
  # of security should the database be compromised. Setting this higher makes registration/login
  # consume more CPU resources but makes it harder to brute force password hashes. This value
  # can be lowered if performing tests or on embedded Dendrite instances (e.g WASM builds).
  bcrypt_cost: 10

  # The length of time that a token issued for a relying party from
  # /_matrix/client/r0/user/{userId}/openid/request_token endpoint
  # is considered to be valid in milliseconds.
  # The default lifetime is 3600000ms (60 minutes).
  # openid_token_lifetime_ms: 3600000

  # Users who register on this homeserver will automatically be joined to the rooms listed under "auto_join_rooms" option.
  # By default, any room aliases included in this list will be created as a publicly joinable room
  # when the first user registers for the homeserver. If the room already exists,
  # make certain it is a publicly joinable room, i.e. the join rule of the room must be set to 'public'.
  # As Spaces are just rooms under the hood, Space aliases may also be used.
  auto_join_rooms:
  #  - "#main:matrix.org"

  # The number of workers to start for the DeviceListUpdater. Defaults to 8.
  # This only needs updating if the "InputDeviceListUpdate" stream keeps growing indefinitely.
  # worker_count: 8

# Configuration for Opentracing.
# See https://github.com/element-hq/dendrite/tree/master/docs/tracing for information on
# how this works and how to set it up.
tracing:
  enabled: true
  jaeger:
    serviceName: "dendrite"
    disabled: false
    rpc_metrics: true
    tags:
      - key: "env"
        value: "local"  # ✅ 正确：结构体形式
    sampler: 
      type: const
      param: 1
    reporter: 
     localAgentHostPort: ""  # 不使用 UDP agent，因为你要用 OTLP
     collectorEndpoint: "http://************:14268/api/traces"  # OTLP endpoint
    headers: null
    baggage_restrictions: null
    throttler: null

# Logging configuration. The "std" logging type controls the logs being sent to
# stdout. The "file" logging type controls logs being written to a log folder on
# the disk. Supported log levels are "debug", "info", "warn", "error".
logging:
  - type: std
    level: info
  - type: file
    level: info
    params:
      path: ./logs
