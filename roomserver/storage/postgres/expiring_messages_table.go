package postgres

import (
	"context"
	"database/sql"
	"time"

	"github.com/element-hq/dendrite/internal/sqlutil"
	"github.com/element-hq/dendrite/roomserver/storage/shared"
	"github.com/sirupsen/logrus"
)

const expiringMessagesSchema = `
-- 存储需要阅后即焚的消息
CREATE TABLE IF NOT EXISTS roomserver_expiring_messages (
    -- 消息事件ID
    event_id TEXT PRIMARY KEY,
    -- 房间ID
    room_id TEXT NOT NULL,
    -- 发送者用户ID
    sender_id TEXT NOT NULL,
    -- 过期时间戳 (Unix timestamp in seconds)
    expire_ts BIGINT NOT NULL,
    -- 创建时间
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    -- 是否已处理
    processed BOOLEAN NOT NULL DEFAULT FALSE
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_expire_ts_idx ON roomserver_expiring_messages (expire_ts) WHERE NOT processed;
CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_room_id_idx ON roomserver_expiring_messages (room_id);
CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_processed_idx ON roomserver_expiring_messages (processed);
`

const insertExpiringMessageSQL = `
    INSERT INTO roomserver_expiring_messages (event_id, room_id, sender_id, expire_ts)
    VALUES ($1, $2, $3, $4)
`

const selectExpiredMessagesSQL = `
    SELECT event_id, room_id, sender_id, expire_ts
    FROM roomserver_expiring_messages
    WHERE expire_ts <= $1 AND NOT processed
    ORDER BY expire_ts ASC
    LIMIT $2
`

const selectAndMarkExpiredMessagesSQL = `
    UPDATE roomserver_expiring_messages
    SET status = 'processing', updated_at = NOW()
    WHERE event_id IN (
        SELECT event_id
        FROM roomserver_expiring_messages
        WHERE expire_ts <= $1 AND status = 'pending'
        ORDER BY expire_ts ASC
        LIMIT $2
        FOR UPDATE SKIP LOCKED
    )
    RETURNING event_id, room_id, sender_id, expire_ts
`

const markMessageStatusSQL = `
    UPDATE roomserver_expiring_messages
    SET status = $2, updated_at = NOW()
    WHERE event_id = $1
`

const markMessageProcessedSQL = `
    UPDATE roomserver_expiring_messages
    SET processed = TRUE
    WHERE event_id = $1
`

const deleteProcessedMessagesSQL = `
    DELETE FROM roomserver_expiring_messages
    WHERE processed = TRUE AND created_at < $1
`

const selectExpiringMessageSQL = `
    SELECT event_id, room_id, sender_id, expire_ts, processed
    FROM roomserver_expiring_messages
    WHERE event_id = $1
`

type expiringMessagesStatements struct {
	insertExpiringMessageStmt        *sql.Stmt
	selectExpiredMessagesStmt        *sql.Stmt
	markMessageProcessedStmt         *sql.Stmt
	deleteProcessedMessagesStmt      *sql.Stmt
	selectExpiringMessageStmt        *sql.Stmt
	selectAndMarkExpiredMessagesStmt *sql.Stmt
	markMessageStatusStmt            *sql.Stmt
}

func CreateExpiringMessagesTable(db *sql.DB) error {
	_, err := db.Exec(expiringMessagesSchema)
	return err
}

func PrepareExpiringMessagesTable(db *sql.DB) (*expiringMessagesStatements, error) {
	s := &expiringMessagesStatements{}

	return s, sqlutil.StatementList{
		{&s.insertExpiringMessageStmt, insertExpiringMessageSQL},
		{&s.selectExpiredMessagesStmt, selectExpiredMessagesSQL},
		{&s.markMessageProcessedStmt, markMessageProcessedSQL},
		{&s.deleteProcessedMessagesStmt, deleteProcessedMessagesSQL},
		{&s.selectExpiringMessageStmt, selectExpiringMessageSQL},
		{&s.selectAndMarkExpiredMessagesStmt, selectAndMarkExpiredMessagesSQL},
		{&s.markMessageStatusStmt, markMessageStatusSQL},
	}.Prepare(db)
}

// InsertExpiringMessage 插入一个需要过期的消息
func (s *expiringMessagesStatements) InsertExpiringMessage(
	ctx context.Context, txn *sql.Tx, eventID, roomID, senderID string, expireTS int64,
) error {
	stmt := sqlutil.TxStmt(txn, s.insertExpiringMessageStmt)
	_, err := stmt.ExecContext(ctx, eventID, roomID, senderID, expireTS)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"event_id":  eventID,
			"room_id":   roomID,
			"sender_id": senderID,
			"expire_ts": expireTS,
		}).Error("Failed to insert expiring message")
	}
	return err
}

// SelectExpiredMessages 查询已过期的消息
func (s *expiringMessagesStatements) SelectExpiredMessages(
	ctx context.Context, txn *sql.Tx, currentTS int64, limit int,
) ([]shared.ExpiringMessage, error) {
	stmt := sqlutil.TxStmt(txn, s.selectExpiredMessagesStmt)
	rows, err := stmt.QueryContext(ctx, currentTS, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var messages []shared.ExpiringMessage
	for rows.Next() {
		var msg shared.ExpiringMessage
		if err := rows.Scan(&msg.EventID, &msg.RoomID, &msg.SenderID, &msg.ExpireTS); err != nil {
			return nil, err
		}
		messages = append(messages, msg)
	}
	return messages, rows.Err()
}

// MarkMessageProcessed 标记消息为已处理
func (s *expiringMessagesStatements) MarkMessageProcessed(
	ctx context.Context, txn *sql.Tx, eventID string,
) error {
	stmt := sqlutil.TxStmt(txn, s.markMessageProcessedStmt)
	_, err := stmt.ExecContext(ctx, eventID)
	return err
}

// DeleteProcessedMessages 删除已处理的旧消息记录
func (s *expiringMessagesStatements) DeleteProcessedMessages(
	ctx context.Context, txn *sql.Tx, beforeTime time.Time,
) error {
	stmt := sqlutil.TxStmt(txn, s.deleteProcessedMessagesStmt)
	_, err := stmt.ExecContext(ctx, beforeTime)
	return err
}

// SelectExpiringMessage 查询特定的过期消息
func (s *expiringMessagesStatements) SelectExpiringMessage(
	ctx context.Context, txn *sql.Tx, eventID string,
) (*shared.ExpiringMessage, error) {
	stmt := sqlutil.TxStmt(txn, s.selectExpiringMessageStmt)
	var msg shared.ExpiringMessage
	err := stmt.QueryRowContext(ctx, eventID).Scan(
		&msg.EventID, &msg.RoomID, &msg.SenderID, &msg.ExpireTS, &msg.Processed,
	)
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return &msg, nil
}

// SelectAndMarkExpiredMessages 原子性地查询并标记过期消息为"处理中"状态
// 使用 FOR UPDATE SKIP LOCKED 实现高并发无锁处理
func (s *expiringMessagesStatements) SelectAndMarkExpiredMessages(
	ctx context.Context, txn *sql.Tx, currentTS int64, limit int,
) ([]shared.ExpiringMessage, error) {
	stmt := sqlutil.TxStmt(txn, s.selectAndMarkExpiredMessagesStmt)
	rows, err := stmt.QueryContext(ctx, currentTS, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var messages []shared.ExpiringMessage
	for rows.Next() {
		var msg shared.ExpiringMessage
		if err := rows.Scan(&msg.EventID, &msg.RoomID, &msg.SenderID, &msg.ExpireTS); err != nil {
			return nil, err
		}
		// 设置状态为处理中
		msg.Status = shared.ExpiringMessageStatusProcessing
		msg.Processed = false // 还未完成处理
		messages = append(messages, msg)
	}

	return messages, rows.Err()
}

// MarkMessageStatus 更新消息状态
func (s *expiringMessagesStatements) MarkMessageStatus(
	ctx context.Context, txn *sql.Tx, eventID, status string,
) error {
	stmt := sqlutil.TxStmt(txn, s.markMessageStatusStmt)
	_, err := stmt.ExecContext(ctx, eventID, status)
	return err
}

// MarkMessageStatusWithError 更新消息状态并记录错误信息
func (s *expiringMessagesStatements) MarkMessageStatusWithError(
	ctx context.Context, txn *sql.Tx, eventID, status, errorMessage string,
) error {
	// 需要扩展SQL来支持错误信息
	stmt := sqlutil.TxStmt(txn, s.markMessageStatusStmt)
	_, err := stmt.ExecContext(ctx, eventID, status)
	// TODO: 添加错误信息更新逻辑
	_ = errorMessage // 暂时忽略错误信息
	return err
}
