// Copyright 2025 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package deltas

import (
	"context"
	"database/sql"
	"fmt"
)

func UpAddExpiringMessagesQueueFields(ctx context.Context, tx *sql.Tx) error {
	// 添加新字段以支持队列模式
	_, err := tx.ExecContext(ctx, `
		ALTER TABLE roomserver_expiring_messages 
		ADD COLUMN IF NOT EXISTS pushed BOOLEAN NOT NULL DEFAULT FALSE;
		
		ALTER TABLE roomserver_expiring_messages 
		ADD COLUMN IF NOT EXISTS retry_count INTEGER NOT NULL DEFAULT 0;
		
		ALTER TABLE roomserver_expiring_messages 
		ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP NOT NULL DEFAULT NOW();
	`)
	if err != nil {
		return fmt.Errorf("failed to add new columns: %w", err)
	}

	// 更新现有索引
	_, err = tx.ExecContext(ctx, `
		-- 删除旧索引
		DROP INDEX IF EXISTS roomserver_expiring_messages_expire_ts_idx;
		
		-- 创建新的优化索引
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_expire_ts_idx 
		ON roomserver_expiring_messages (expire_ts) 
		WHERE NOT processed AND NOT pushed;
		
		-- 添加新索引
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_pushed_idx 
		ON roomserver_expiring_messages (pushed);
	`)
	if err != nil {
		return fmt.Errorf("failed to update indexes: %w", err)
	}

	return nil
}

func DownAddExpiringMessagesQueueFields(ctx context.Context, tx *sql.Tx) error {
	// 删除新添加的字段
	_, err := tx.ExecContext(ctx, `
		ALTER TABLE roomserver_expiring_messages DROP COLUMN IF EXISTS pushed;
		ALTER TABLE roomserver_expiring_messages DROP COLUMN IF EXISTS retry_count;
		ALTER TABLE roomserver_expiring_messages DROP COLUMN IF EXISTS updated_at;
	`)
	if err != nil {
		return fmt.Errorf("failed to remove columns: %w", err)
	}

	// 恢复原始索引
	_, err = tx.ExecContext(ctx, `
		-- 删除新索引
		DROP INDEX IF EXISTS roomserver_expiring_messages_pushed_idx;
		
		-- 恢复原始索引
		DROP INDEX IF EXISTS roomserver_expiring_messages_expire_ts_idx;
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_expire_ts_idx 
		ON roomserver_expiring_messages (expire_ts) 
		WHERE NOT processed;
	`)
	if err != nil {
		return fmt.Errorf("failed to restore indexes: %w", err)
	}

	return nil
}
