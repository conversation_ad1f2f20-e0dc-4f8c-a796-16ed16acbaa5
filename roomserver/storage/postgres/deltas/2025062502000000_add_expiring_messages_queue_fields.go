// Copyright 2025 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package deltas

import (
	"context"
	"database/sql"
	"fmt"
)

func UpAddExpiringMessagesQueueFields(ctx context.Context, tx *sql.Tx) error {
	// 首先检查表是否存在
	var tableName string
	err := tx.QueryRowContext(ctx,
		"SELECT table_name FROM information_schema.tables WHERE table_name = 'roomserver_expiring_messages'").Scan(&tableName)
	if err != nil {
		if err == sql.ErrNoRows {
			// 表不存在，跳过迁移
			return nil
		}
		return fmt.Errorf("failed to check table existence: %w", err)
	}

	// 检查字段是否已存在
	var columnName string
	err = tx.QueryRowContext(ctx,
		"SELECT column_name FROM information_schema.columns WHERE table_name = 'roomserver_expiring_messages' AND column_name = 'pushed'").Scan(&columnName)
	if err == nil {
		// 字段已存在，跳过迁移
		return nil
	}
	if err != sql.ErrNoRows {
		return fmt.Errorf("failed to check column existence: %w", err)
	}

	// 添加新字段以支持队列模式和状态管理
	_, err = tx.ExecContext(ctx, `
		ALTER TABLE roomserver_expiring_messages
		ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'pending';
	`)
	if err != nil {
		return fmt.Errorf("failed to add status column: %w", err)
	}

	_, err = tx.ExecContext(ctx, `
		ALTER TABLE roomserver_expiring_messages
		ADD COLUMN retry_count INTEGER NOT NULL DEFAULT 0;
	`)
	if err != nil {
		return fmt.Errorf("failed to add retry_count column: %w", err)
	}

	_, err = tx.ExecContext(ctx, `
		ALTER TABLE roomserver_expiring_messages
		ADD COLUMN updated_at TIMESTAMP NOT NULL DEFAULT NOW();
	`)
	if err != nil {
		return fmt.Errorf("failed to add updated_at column: %w", err)
	}

	_, err = tx.ExecContext(ctx, `
		ALTER TABLE roomserver_expiring_messages
		ADD COLUMN error_message TEXT;
	`)
	if err != nil {
		return fmt.Errorf("failed to add error_message column: %w", err)
	}

	// 更新现有索引以支持状态字段
	_, err = tx.ExecContext(ctx, `DROP INDEX IF EXISTS roomserver_expiring_messages_expire_ts_idx;`)
	if err != nil {
		return fmt.Errorf("failed to drop old index: %w", err)
	}

	_, err = tx.ExecContext(ctx, `
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_expire_ts_status_idx
		ON roomserver_expiring_messages (expire_ts, status)
		WHERE status = 'pending';
	`)
	if err != nil {
		return fmt.Errorf("failed to create new expire_ts_status index: %w", err)
	}

	_, err = tx.ExecContext(ctx, `
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_status_idx
		ON roomserver_expiring_messages (status);
	`)
	if err != nil {
		return fmt.Errorf("failed to create status index: %w", err)
	}

	_, err = tx.ExecContext(ctx, `
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_updated_at_idx
		ON roomserver_expiring_messages (updated_at);
	`)
	if err != nil {
		return fmt.Errorf("failed to create updated_at index: %w", err)
	}

	return nil
}

func DownAddExpiringMessagesQueueFields(ctx context.Context, tx *sql.Tx) error {
	// 删除新添加的字段
	_, err := tx.ExecContext(ctx, `
		ALTER TABLE roomserver_expiring_messages DROP COLUMN IF EXISTS pushed;
		ALTER TABLE roomserver_expiring_messages DROP COLUMN IF EXISTS retry_count;
		ALTER TABLE roomserver_expiring_messages DROP COLUMN IF EXISTS updated_at;
	`)
	if err != nil {
		return fmt.Errorf("failed to remove columns: %w", err)
	}

	// 恢复原始索引
	_, err = tx.ExecContext(ctx, `
		-- 删除新索引
		DROP INDEX IF EXISTS roomserver_expiring_messages_pushed_idx;
		
		-- 恢复原始索引
		DROP INDEX IF EXISTS roomserver_expiring_messages_expire_ts_idx;
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_expire_ts_idx 
		ON roomserver_expiring_messages (expire_ts) 
		WHERE NOT processed;
	`)
	if err != nil {
		return fmt.Errorf("failed to restore indexes: %w", err)
	}

	return nil
}
