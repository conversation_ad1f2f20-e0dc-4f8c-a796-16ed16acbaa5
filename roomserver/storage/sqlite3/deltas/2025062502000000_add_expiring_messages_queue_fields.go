// Copyright 2025 New Vector Ltd.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

package deltas

import (
	"context"
	"database/sql"
	"fmt"
)

func UpAddExpiringMessagesQueueFields(ctx context.Context, tx *sql.Tx) error {
	// SQLite需要重建表来添加新字段
	_, err := tx.ExecContext(ctx, `
		-- 重命名原表
		ALTER TABLE roomserver_expiring_messages RENAME TO roomserver_expiring_messages_old;
		
		-- 创建新表结构
		CREATE TABLE IF NOT EXISTS roomserver_expiring_messages (
			-- 消息事件ID
			event_id TEXT PRIMARY KEY,
			-- 房间ID
			room_id TEXT NOT NULL,
			-- 发送者用户ID
			sender_id TEXT NOT NULL,
			-- 过期时间戳 (Unix timestamp in seconds)
			expire_ts INTEGER NOT NULL,
			-- 创建时间
			created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
			-- 是否已处理
			processed INTEGER NOT NULL DEFAULT 0,
			-- 是否已推送到队列
			pushed INTEGER NOT NULL DEFAULT 0,
			-- 重试次数
			retry_count INTEGER NOT NULL DEFAULT 0,
			-- 最后更新时间
			updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
		);
		
		-- 迁移数据
		INSERT INTO roomserver_expiring_messages (
			event_id, room_id, sender_id, expire_ts, created_at, processed, pushed, retry_count, updated_at
		) SELECT 
			event_id, room_id, sender_id, expire_ts, created_at, processed, 0, 0, created_at
		FROM roomserver_expiring_messages_old;
		
		-- 删除旧表
		DROP TABLE roomserver_expiring_messages_old;
		
		-- 创建索引
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_expire_ts_idx 
		ON roomserver_expiring_messages (expire_ts) 
		WHERE processed = 0 AND pushed = 0;
		
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_room_id_idx 
		ON roomserver_expiring_messages (room_id);
		
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_processed_idx 
		ON roomserver_expiring_messages (processed);
		
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_pushed_idx 
		ON roomserver_expiring_messages (pushed);
	`)
	if err != nil {
		return fmt.Errorf("failed to execute upgrade: %w", err)
	}
	return nil
}

func DownAddExpiringMessagesQueueFields(ctx context.Context, tx *sql.Tx) error {
	// SQLite需要重建表来移除字段
	_, err := tx.ExecContext(ctx, `
		-- 重命名当前表
		ALTER TABLE roomserver_expiring_messages RENAME TO roomserver_expiring_messages_new;
		
		-- 创建原始表结构
		CREATE TABLE IF NOT EXISTS roomserver_expiring_messages (
			-- 消息事件ID
			event_id TEXT PRIMARY KEY,
			-- 房间ID
			room_id TEXT NOT NULL,
			-- 发送者用户ID
			sender_id TEXT NOT NULL,
			-- 过期时间戳 (Unix timestamp in seconds)
			expire_ts INTEGER NOT NULL,
			-- 创建时间
			created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
			-- 是否已处理
			processed INTEGER NOT NULL DEFAULT 0
		);
		
		-- 迁移数据（只保留原有字段）
		INSERT INTO roomserver_expiring_messages (
			event_id, room_id, sender_id, expire_ts, created_at, processed
		) SELECT 
			event_id, room_id, sender_id, expire_ts, created_at, processed
		FROM roomserver_expiring_messages_new;
		
		-- 删除新表
		DROP TABLE roomserver_expiring_messages_new;
		
		-- 创建原始索引
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_expire_ts_idx 
		ON roomserver_expiring_messages (expire_ts) 
		WHERE processed = 0;
		
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_room_id_idx 
		ON roomserver_expiring_messages (room_id);
		
		CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_processed_idx 
		ON roomserver_expiring_messages (processed);
	`)
	if err != nil {
		return fmt.Errorf("failed to execute downgrade: %w", err)
	}
	return nil
}
