package sqlite3

import (
	"context"
	"database/sql"
	"time"

	"github.com/element-hq/dendrite/internal/sqlutil"
	"github.com/element-hq/dendrite/roomserver/storage/shared"
	"github.com/sirupsen/logrus"
)

const expiringMessagesSchema = `
-- 存储需要阅后即焚的消息
CREATE TABLE IF NOT EXISTS roomserver_expiring_messages (
    -- 消息事件ID
    event_id TEXT PRIMARY KEY,
    -- 房间ID
    room_id TEXT NOT NULL,
    -- 发送者用户ID
    sender_id TEXT NOT NULL,
    -- 过期时间戳 (Unix timestamp in seconds)
    expire_ts INTEGER NOT NULL,
    -- 创建时间
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    -- 是否已处理
    processed INTEGER NOT NULL DEFAULT 0,
    -- 是否已推送到队列
    pushed INTEGER NOT NULL DEFAULT 0,
    -- 重试次数
    retry_count INTEGER NOT NULL DEFAULT 0,
    -- 最后更新时间
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以优化查询性能
CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_expire_ts_idx ON roomserver_expiring_messages (expire_ts) WHERE processed = 0 AND pushed = 0;
CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_room_id_idx ON roomserver_expiring_messages (room_id);
CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_processed_idx ON roomserver_expiring_messages (processed);
CREATE INDEX IF NOT EXISTS roomserver_expiring_messages_pushed_idx ON roomserver_expiring_messages (pushed);
`

const insertExpiringMessageSQL = `
    INSERT INTO roomserver_expiring_messages (event_id, room_id, sender_id, expire_ts)
    VALUES ($1, $2, $3, $4)
`

const selectExpiredMessagesSQL = `
    SELECT event_id, room_id, sender_id, expire_ts
    FROM roomserver_expiring_messages
    WHERE expire_ts <= $1 AND processed = 0 AND pushed = 0
    ORDER BY expire_ts ASC
    LIMIT $2
`

const markMessageProcessedSQL = `
    UPDATE roomserver_expiring_messages
    SET processed = 1
    WHERE event_id = $1
`

const deleteProcessedMessagesSQL = `
    DELETE FROM roomserver_expiring_messages
    WHERE processed = 1 AND created_at < $1
`

const selectExpiringMessageSQL = `
    SELECT event_id, room_id, sender_id, expire_ts, processed
    FROM roomserver_expiring_messages
    WHERE event_id = $1
`

const markMessagePushedSQL = `
    UPDATE roomserver_expiring_messages
    SET pushed = 1, updated_at = CURRENT_TIMESTAMP
    WHERE event_id = $1 AND pushed = 0 AND processed = 0
`

const rollbackMessagePushedSQL = `
    UPDATE roomserver_expiring_messages
    SET pushed = 0, updated_at = CURRENT_TIMESTAMP
    WHERE event_id = $1
`

const incrementRetryCountSQL = `
    UPDATE roomserver_expiring_messages
    SET retry_count = retry_count + 1, updated_at = CURRENT_TIMESTAMP
    WHERE event_id = $1
`

type expiringMessagesStatements struct {
	insertExpiringMessageStmt   *sql.Stmt
	selectExpiredMessagesStmt   *sql.Stmt
	markMessageProcessedStmt    *sql.Stmt
	deleteProcessedMessagesStmt *sql.Stmt
	selectExpiringMessageStmt   *sql.Stmt
	markMessagePushedStmt       *sql.Stmt
	rollbackMessagePushedStmt   *sql.Stmt
	incrementRetryCountStmt     *sql.Stmt
}

func CreateExpiringMessagesTable(db *sql.DB) error {
	_, err := db.Exec(expiringMessagesSchema)
	return err
}

func PrepareExpiringMessagesTable(db *sql.DB) (*expiringMessagesStatements, error) {
	s := &expiringMessagesStatements{}

	return s, sqlutil.StatementList{
		{&s.insertExpiringMessageStmt, insertExpiringMessageSQL},
		{&s.selectExpiredMessagesStmt, selectExpiredMessagesSQL},
		{&s.markMessageProcessedStmt, markMessageProcessedSQL},
		{&s.deleteProcessedMessagesStmt, deleteProcessedMessagesSQL},
		{&s.selectExpiringMessageStmt, selectExpiringMessageSQL},
		{&s.markMessagePushedStmt, markMessagePushedSQL},
		{&s.rollbackMessagePushedStmt, rollbackMessagePushedSQL},
		{&s.incrementRetryCountStmt, incrementRetryCountSQL},
	}.Prepare(db)
}

// InsertExpiringMessage 插入一个需要过期的消息
func (s *expiringMessagesStatements) InsertExpiringMessage(
	ctx context.Context, txn *sql.Tx, eventID, roomID, senderID string, expireTS int64,
) error {
	stmt := sqlutil.TxStmt(txn, s.insertExpiringMessageStmt)
	_, err := stmt.ExecContext(ctx, eventID, roomID, senderID, expireTS)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"event_id":  eventID,
			"room_id":   roomID,
			"sender_id": senderID,
			"expire_ts": expireTS,
		}).Error("Failed to insert expiring message")
	}
	return err
}

// SelectExpiredMessages 查询已过期的消息
func (s *expiringMessagesStatements) SelectExpiredMessages(
	ctx context.Context, txn *sql.Tx, currentTS int64, limit int,
) ([]shared.ExpiringMessage, error) {
	stmt := sqlutil.TxStmt(txn, s.selectExpiredMessagesStmt)
	rows, err := stmt.QueryContext(ctx, currentTS, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var messages []shared.ExpiringMessage
	for rows.Next() {
		var msg shared.ExpiringMessage
		if err := rows.Scan(&msg.EventID, &msg.RoomID, &msg.SenderID, &msg.ExpireTS); err != nil {
			return nil, err
		}
		messages = append(messages, msg)
	}
	return messages, rows.Err()
}

// MarkMessageProcessed 标记消息为已处理
func (s *expiringMessagesStatements) MarkMessageProcessed(
	ctx context.Context, txn *sql.Tx, eventID string,
) error {
	stmt := sqlutil.TxStmt(txn, s.markMessageProcessedStmt)
	_, err := stmt.ExecContext(ctx, eventID)
	return err
}

// DeleteProcessedMessages 删除已处理的旧消息记录
func (s *expiringMessagesStatements) DeleteProcessedMessages(
	ctx context.Context, txn *sql.Tx, beforeTime time.Time,
) error {
	stmt := sqlutil.TxStmt(txn, s.deleteProcessedMessagesStmt)
	_, err := stmt.ExecContext(ctx, beforeTime)
	return err
}

// SelectExpiringMessage 查询特定的过期消息
func (s *expiringMessagesStatements) SelectExpiringMessage(
	ctx context.Context, txn *sql.Tx, eventID string,
) (*shared.ExpiringMessage, error) {
	stmt := sqlutil.TxStmt(txn, s.selectExpiringMessageStmt)
	var msg shared.ExpiringMessage
	var processed int
	err := stmt.QueryRowContext(ctx, eventID).Scan(
		&msg.EventID, &msg.RoomID, &msg.SenderID, &msg.ExpireTS, &processed,
	)
	if err == sql.ErrNoRows {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	msg.Processed = processed != 0
	return &msg, nil
}

// MarkMessagePushed 原子操作：标记消息为已推送（仅当未推送且未处理时）
func (s *expiringMessagesStatements) MarkMessagePushed(
	ctx context.Context, txn *sql.Tx, eventID string,
) (bool, error) {
	// SQLite版本的原子更新
	stmt := sqlutil.TxStmt(txn, s.markMessagePushedStmt)
	result, err := stmt.ExecContext(ctx, eventID)
	if err != nil {
		return false, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return false, err
	}

	return rowsAffected > 0, nil
}

// RollbackMessagePushed 回滚消息推送状态
func (s *expiringMessagesStatements) RollbackMessagePushed(
	ctx context.Context, txn *sql.Tx, eventID string,
) error {
	stmt := sqlutil.TxStmt(txn, s.rollbackMessagePushedStmt)
	_, err := stmt.ExecContext(ctx, eventID)
	return err
}

// IncrementRetryCount 增加重试次数
func (s *expiringMessagesStatements) IncrementRetryCount(
	ctx context.Context, txn *sql.Tx, eventID string,
) error {
	stmt := sqlutil.TxStmt(txn, s.incrementRetryCountStmt)
	_, err := stmt.ExecContext(ctx, eventID)
	return err
}
