// Copyright 2024 New Vector Ltd.
// Copyright 2020 The Matrix.org Foundation C.I.C.
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

//go:build !wasm
// +build !wasm

package storage

import (
	"context"

	"github.com/element-hq/dendrite/internal/caching"
	"github.com/element-hq/dendrite/internal/sqlutil"
	"github.com/element-hq/dendrite/roomserver/storage/postgres"
	"github.com/element-hq/dendrite/setup/config"
)

// Open opens a database connection.
func Open(ctx context.Context, conMan *sqlutil.Connections, dbProperties *config.DatabaseOptions, cache caching.RoomServerCaches) (Database, error) {
	return postgres.Open(ctx, conMan, dbProperties, cache)

	// 多余的判断 暂时不要 
	// switch {
	// case dbProperties.ConnectionString.IsSQLite():
	// 	return sqlite3.Open(ctx, conMan, dbProperties, cache)
	// case dbProperties.ConnectionString.IsPostgres():
	// 	return postgres.Open(ctx, conMan, dbProperties, cache)
	// default:
	// 	return nil, fmt.Errorf("unexpected database type")
	// }
}
