package expiring_messages

import (
	"context"
	"sync"
	"time"

	"github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/roomserver/storage"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/sirupsen/logrus"
)

// Service 全局过期消息服务实例
var (
	globalService *ExpiringMessagesService
	serviceMutex  sync.Mutex
)

// InitializeService 初始化全局过期消息服务
func InitializeService(db storage.Database, rsAPI api.RoomserverInternalAPI, cfg *config.Dendrite) {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if globalService == nil {
		globalService = NewExpiringMessagesServiceWithDefaults(db, rsAPI,cfg)
		logrus.Info("阅后即焚消息服务已初始化（延迟启动）")
		logrus.Info("服务已准备就绪，请在 roomserver 完全初始化后调用 StartService()")
	}
}

// InitializeServiceFromConfig 从配置初始化全局过期消息服务（推荐方式）
func InitializeServiceFromConfig(db storage.Database, rsAPI api.RoomserverInternalAPI, cfg *config.Dendrite) error {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if globalService != nil {
		logrus.Warn("阅后即焚消息服务已经初始化，跳过重复初始化")
		return nil
	}

	// 从配置创建服务
	service, err := CreateServiceFromConfig(db, rsAPI, cfg)
	if err != nil {
		logrus.WithError(err).Error("从配置创建阅后即焚消息服务失败")
		return err
	}

	globalService = service
	logrus.Info("阅后即焚消息服务已从配置初始化（延迟启动）")

	// 不要立即启动，等待手动调用 StartService
	logrus.Info("服务已准备就绪，请在 roomserver 完全初始化后调用 StartService()")

	return nil
}

// StartService 启动全局过期消息服务
func StartService() {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if globalService != nil {
		logrus.Info("正在启动阅后即焚消息服务...")
		go func() {
			// 添加延迟，确保 roomserver 完全初始化
			// time.Sleep(5 * time.Second)
			logrus.Info("启动阅后即焚消息服务")
			globalService.Start()
		}()
		logrus.Info("阅后即焚消息服务启动任务已提交")
	} else {
		logrus.Error("过期消息服务未初始化，无法启动")
	}
}

// StartServiceImmediately 立即启动服务（用于确保 roomserver 已完全初始化的场景）
func StartServiceImmediately() {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if globalService != nil {
		logrus.Info("立即启动阅后即焚消息服务")
		go globalService.Start()
		logrus.Info("阅后即焚消息服务已启动")
	} else {
		logrus.Error("过期消息服务未初始化，无法启动")
	}
}

// StartServiceWithDelay 延迟指定时间后启动服务
func StartServiceWithDelay(delay time.Duration) {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if globalService != nil {
		logrus.WithField("delay", delay).Info("计划延迟启动阅后即焚消息服务")
		go func() {
			time.Sleep(delay)
			logrus.Info("延迟时间到达，启动阅后即焚消息服务")
			globalService.Start()
		}()
		logrus.Info("阅后即焚消息服务延迟启动任务已提交")
	} else {
		logrus.Error("过期消息服务未初始化，无法启动")
	}
}

// StopService 停止全局过期消息服务
func StopService() {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()

	if globalService != nil {
		globalService.Stop()
		logrus.Info("阅后即焚消息服务已停止")
	}
}

// AddExpiringMessage 添加过期消息到全局服务
func AddExpiringMessage(ctx context.Context, eventID, roomID, senderID string, expireTS int64) error {
	serviceMutex.Lock()
	service := globalService
	serviceMutex.Unlock()

	if service == nil {
		logrus.Warn("过期消息服务未初始化，跳过添加过期消息")
		return nil
	}

	return service.AddExpiringMessage(ctx, eventID, roomID, senderID, expireTS)
}

// GetService 获取全局服务实例（用于测试）
func GetService() *ExpiringMessagesService {
	serviceMutex.Lock()
	defer serviceMutex.Unlock()
	return globalService
}
