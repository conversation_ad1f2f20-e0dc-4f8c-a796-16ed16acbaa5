# 阅后即焚消息队列改进方案

## 问题分析

### 当前实现的问题

1. **资源浪费**：每台机器都运行定时器，但只有一台能获得锁进行处理
2. **锁竞争**：多台机器频繁竞争Redis锁，浪费网络和CPU资源
3. **实例ID冲突**：使用`serverName-PID`可能在多台机器上重复
4. **处理效率低**：同一时间只有一台机器在工作，无法充分利用集群资源
5. **扩展性差**：无法根据负载动态调整处理能力

### 改进目标

1. **提高资源利用率**：让多台机器协同工作
2. **减少锁竞争**：使用队列模式减少不必要的锁操作
3. **增强可扩展性**：支持动态调整生产者和消费者数量
4. **提高可靠性**：支持重试机制和故障恢复
5. **便于监控**：提供队列状态和性能指标

## 解决方案：生产者-消费者队列模式

### 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   生产者机器     │    │   Redis队列      │    │   消费者机器     │
│                │    │                │    │                │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 定时扫描器   │ │───▶│ │  主队列      │ │◀───│ │ 消费者1     │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                │    │                │    │                │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 状态标记器   │ │    │ │  重试队列    │ │    │ │ 消费者2     │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                │    │                │    │                │
│                │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│                │    │ │  失败队列    │ │    │ │ 消费者N     │ │
│                │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件

#### 1. 生产者（Producer）
- **职责**：扫描过期消息，推送到Redis队列
- **特点**：
  - 使用分布式锁确保只有一个生产者在扫描
  - 原子操作标记消息为"已推送"状态
  - 支持批量处理提高效率

#### 2. 消费者（Consumer）
- **职责**：从Redis队列消费任务，处理过期消息
- **特点**：
  - 多个消费者并行工作
  - 支持重试机制
  - 失败任务自动进入重试队列

#### 3. Redis队列管理器
- **主队列**：存储待处理的过期消息任务
- **处理队列**：正在处理的任务（用于故障恢复）
- **重试队列**：处理失败需要重试的任务
- **失败队列**：超过最大重试次数的任务

### 数据库改进

#### 新增字段
```sql
-- 是否已推送到队列
pushed BOOLEAN NOT NULL DEFAULT FALSE,
-- 重试次数
retry_count INTEGER NOT NULL DEFAULT 0,
-- 最后更新时间
updated_at TIMESTAMP NOT NULL DEFAULT NOW()
```

#### 原子操作
```sql
-- 原子标记为已推送（仅当未推送且未处理时）
UPDATE roomserver_expiring_messages
SET pushed = TRUE, updated_at = NOW()
WHERE event_id = $1 AND NOT pushed AND NOT processed
```

### 实例ID改进

#### 新的生成策略
```go
// 组合：服务器名-IP-主机名-PID-UUID
instanceID := fmt.Sprintf("%s-%s-%s-%d-%s", 
    serverName, ip, hostname, os.Getpid(), uuid)
```

#### 唯一性保证
- **IP地址**：区分不同机器
- **主机名**：额外的机器标识
- **PID**：区分同机器不同进程
- **UUID**：确保绝对唯一性

## 部署模式

### 1. 专用生产者模式
```yaml
# 机器1：专门的生产者
is_producer: true
is_consumer: false
consumer_count: 0
check_interval: 5s    # 频繁扫描
```

### 2. 专用消费者模式
```yaml
# 机器2-N：专门的消费者
is_producer: false
is_consumer: true
consumer_count: 5     # 多个消费者
```

### 3. 混合模式
```yaml
# 既生产又消费
is_producer: true
is_consumer: true
consumer_count: 2     # 较少消费者
```

## 性能优化

### 1. 批量处理
- 生产者批量扫描和推送
- 消费者批量确认完成

### 2. 连接池优化
- Redis连接池大小 = 消费者数量 × 2
- 合理设置超时时间

### 3. 队列监控
```go
stats, _ := queueManager.GetQueueStats(ctx)
// {
//   "main": 100,       // 主队列长度
//   "retry": 5,        // 重试队列长度
//   "failed": 2,       // 失败队列长度
//   "processing": 10   // 处理中任务数
// }
```

## 故障恢复

### 1. 启动时恢复
```go
// 将处理队列中的任务移回主队列
queueManager.RecoverProcessingTasks(ctx)
```

### 2. 重试队列处理
```go
// 定期将重试队列中的任务移回主队列
queueManager.ProcessRetryQueue(ctx)
```

### 3. 失败任务处理
- 超过最大重试次数的任务进入失败队列
- 定期清理失败队列中的旧任务
- 支持手动重新处理失败任务

## 监控指标

### 1. 队列指标
- 各队列长度
- 任务处理速率
- 任务等待时间

### 2. 性能指标
- 生产者扫描频率
- 消费者处理延迟
- 重试率和失败率

### 3. 系统指标
- Redis连接数
- 内存使用量
- CPU使用率

## 配置建议

### 生产环境
```yaml
# 高可用配置
producer:
  check_interval: "10s"
  batch_size: 100

consumer:
  consumer_count: 5    # 根据CPU核心数调整
  max_retries: 3

redis:
  pool_size: 15        # 消费者数量 × 3
  addresses:           # Redis集群
    - "redis-1:6379"
    - "redis-2:6379"
    - "redis-3:6379"
```

### 开发环境
```yaml
# 简化配置
producer:
  check_interval: "30s"
  batch_size: 50

consumer:
  consumer_count: 2
  max_retries: 2

redis:
  address: "localhost:6379"
  pool_size: 5
```

## 迁移指南

### 1. 向后兼容
- 保留原有的直接处理模式
- 通过配置开关选择模式

### 2. 渐进式迁移
1. 部署新版本（默认使用传统模式）
2. 配置Redis队列
3. 逐步启用队列模式
4. 监控性能和稳定性
5. 完全切换到队列模式

### 3. 数据库迁移
```sql
-- 添加新字段（向后兼容）
ALTER TABLE roomserver_expiring_messages 
ADD COLUMN pushed BOOLEAN NOT NULL DEFAULT FALSE;

ALTER TABLE roomserver_expiring_messages 
ADD COLUMN retry_count INTEGER NOT NULL DEFAULT 0;

ALTER TABLE roomserver_expiring_messages 
ADD COLUMN updated_at TIMESTAMP NOT NULL DEFAULT NOW();
```

## 总结

这个改进方案解决了原有实现的所有问题：

1. **✅ 资源浪费**：通过队列模式让多台机器协同工作
2. **✅ 锁竞争**：只有生产者需要获取扫描锁，消费者无锁并行
3. **✅ 实例ID冲突**：使用IP+主机名+PID+UUID确保唯一性
4. **✅ 处理效率**：多消费者并行处理，充分利用集群资源
5. **✅ 扩展性**：支持动态调整生产者和消费者数量
6. **✅ 可靠性**：完善的重试机制和故障恢复
7. **✅ 监控性**：丰富的队列状态和性能指标

该方案既保持了向后兼容性，又提供了生产级别的可扩展性和可靠性。
