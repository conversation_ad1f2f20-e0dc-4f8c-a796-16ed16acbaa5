# 数据库迁移状态

## 当前状态

由于您遇到了 `pq: column "pushed" does not exist` 错误，我已经回滚了代码到一个可以工作的状态：

### ✅ 已完成的工作

1. **核心队列架构**：
   - ✅ Redis队列管理器 (`redis_queue.go`)
   - ✅ 生产者-消费者模式
   - ✅ 唯一实例ID生成
   - ✅ 重试机制和故障恢复

2. **数据库迁移文件**：
   - ✅ PostgreSQL迁移：`roomserver/storage/postgres/deltas/2025062502000000_add_expiring_messages_queue_fields.go`
   - ✅ SQLite迁移：`roomserver/storage/sqlite3/deltas/2025062502000000_add_expiring_messages_queue_fields.go`

3. **向后兼容性**：
   - ✅ 保持原有表结构不变
   - ✅ 迁移会自动添加新字段
   - ✅ 代码可以在迁移前后都工作

### 🔄 当前状态

代码现在使用**传统模式**，但包含了所有队列模式的基础设施：

- 表结构：使用原始字段（`event_id`, `room_id`, `sender_id`, `expire_ts`, `created_at`, `processed`）
- 迁移：会在下次启动时自动添加新字段（`pushed`, `retry_count`, `updated_at`）
- 队列功能：已实现但暂时使用简化的状态管理

## 下一步操作

### 1. 启动测试
现在可以安全启动Dendrite，迁移会自动执行：

```bash
# 启动Dendrite，迁移会自动运行
./dendrite
```

### 2. 验证迁移
启动后检查数据库表是否包含新字段：

```sql
-- PostgreSQL
\d roomserver_expiring_messages

-- SQLite
.schema roomserver_expiring_messages
```

应该看到这些字段：
- `pushed BOOLEAN NOT NULL DEFAULT FALSE`
- `retry_count INTEGER NOT NULL DEFAULT 0`
- `updated_at TIMESTAMP NOT NULL DEFAULT NOW()`

### 3. 启用队列模式
迁移完成后，可以在配置中启用队列模式：

```yaml
room_server:
  expiring_messages:
    enabled: true
    use_queue: true        # 启用队列模式
    is_producer: true      # 是否作为生产者
    is_consumer: true      # 是否作为消费者
    consumer_count: 3      # 消费者数量
    max_retries: 3         # 最大重试次数
    redis:
      enabled: true
      address: "localhost:6379"
```

## 迁移详情

### PostgreSQL迁移
```sql
-- 检查表是否存在
SELECT table_name FROM information_schema.tables 
WHERE table_name = 'roomserver_expiring_messages'

-- 检查字段是否已存在
SELECT column_name FROM information_schema.columns 
WHERE table_name = 'roomserver_expiring_messages' AND column_name = 'pushed'

-- 添加新字段
ALTER TABLE roomserver_expiring_messages 
ADD COLUMN pushed BOOLEAN NOT NULL DEFAULT FALSE;

ALTER TABLE roomserver_expiring_messages 
ADD COLUMN retry_count INTEGER NOT NULL DEFAULT 0;

ALTER TABLE roomserver_expiring_messages 
ADD COLUMN updated_at TIMESTAMP NOT NULL DEFAULT NOW();

-- 更新索引
DROP INDEX IF EXISTS roomserver_expiring_messages_expire_ts_idx;
CREATE INDEX roomserver_expiring_messages_expire_ts_idx 
ON roomserver_expiring_messages (expire_ts) 
WHERE NOT processed AND NOT pushed;

CREATE INDEX roomserver_expiring_messages_pushed_idx 
ON roomserver_expiring_messages (pushed);
```

### SQLite迁移
SQLite需要重建表来添加字段：

```sql
-- 重命名原表
ALTER TABLE roomserver_expiring_messages RENAME TO roomserver_expiring_messages_old;

-- 创建新表结构
CREATE TABLE roomserver_expiring_messages (
    event_id TEXT PRIMARY KEY,
    room_id TEXT NOT NULL,
    sender_id TEXT NOT NULL,
    expire_ts INTEGER NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    processed INTEGER NOT NULL DEFAULT 0,
    pushed INTEGER NOT NULL DEFAULT 0,
    retry_count INTEGER NOT NULL DEFAULT 0,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 迁移数据
INSERT INTO roomserver_expiring_messages (
    event_id, room_id, sender_id, expire_ts, created_at, processed, pushed, retry_count, updated_at
) SELECT 
    event_id, room_id, sender_id, expire_ts, created_at, processed, 0, 0, created_at
FROM roomserver_expiring_messages_old;

-- 删除旧表
DROP TABLE roomserver_expiring_messages_old;

-- 创建索引
CREATE INDEX roomserver_expiring_messages_expire_ts_idx 
ON roomserver_expiring_messages (expire_ts) 
WHERE processed = 0 AND pushed = 0;

CREATE INDEX roomserver_expiring_messages_pushed_idx 
ON roomserver_expiring_messages (pushed);
```

## 故障排除

### 如果迁移失败
1. 检查数据库连接权限
2. 检查表是否已存在
3. 手动执行迁移SQL

### 如果需要回滚
迁移文件包含了回滚函数，可以安全回滚：

```sql
-- PostgreSQL回滚
ALTER TABLE roomserver_expiring_messages DROP COLUMN IF EXISTS pushed;
ALTER TABLE roomserver_expiring_messages DROP COLUMN IF EXISTS retry_count;
ALTER TABLE roomserver_expiring_messages DROP COLUMN IF EXISTS updated_at;

-- SQLite回滚（重建表）
-- 见迁移文件中的 DownAddExpiringMessagesQueueFields 函数
```

## 性能影响

### 迁移期间
- PostgreSQL：几乎无停机时间（ALTER TABLE很快）
- SQLite：需要重建表，可能需要几秒钟

### 迁移后
- 新索引会提高查询性能
- 队列模式会显著提高处理效率
- 内存使用量略有增加（Redis连接）

## 监控建议

迁移完成后，监控这些指标：
- 队列长度：`redis-cli LLEN dendrite:expiring_messages`
- 处理延迟：查看日志中的处理时间
- 错误率：监控重试队列和失败队列
- 数据库性能：查询时间和锁等待

现在可以安全启动Dendrite了！
