# 迁移修复说明

## 问题分析

您遇到的错误：
```
Error "pq: column \"status\" does not exist" while preparing statement
```

这是因为我在修改SQL语句时使用了`status`字段，但数据库中还没有这个字段。

## 修复方案

我已经采用了**分阶段迁移**的方案：

### 阶段1：向后兼容（当前状态）
- ✅ SQL语句使用原有的`processed`字段
- ✅ 代码可以正常启动和运行
- ✅ 迁移脚本会在启动时自动执行

### 阶段2：迁移执行（自动）
- 🔄 启动时自动添加`status`、`retry_count`、`updated_at`、`error_message`字段
- 🔄 更新索引以支持新字段

### 阶段3：切换到新字段（手动）
- ⏳ 迁移完成后，可以手动切换到使用新的状态字段

## 当前实现状态

### ✅ 可以正常启动
```sql
-- 当前使用的SQL（向后兼容）
UPDATE roomserver_expiring_messages 
SET processed = TRUE
WHERE event_id IN (
    SELECT event_id 
    FROM roomserver_expiring_messages
    WHERE expire_ts <= $1 AND NOT processed
    ORDER BY expire_ts ASC
    LIMIT $2
    FOR UPDATE SKIP LOCKED
)
RETURNING event_id, room_id, sender_id, expire_ts
```

### 🔄 迁移会自动执行
```sql
-- 迁移会添加这些字段
ALTER TABLE roomserver_expiring_messages 
ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'pending';

ALTER TABLE roomserver_expiring_messages 
ADD COLUMN retry_count INTEGER NOT NULL DEFAULT 0;

ALTER TABLE roomserver_expiring_messages 
ADD COLUMN updated_at TIMESTAMP NOT NULL DEFAULT NOW();

ALTER TABLE roomserver_expiring_messages 
ADD COLUMN error_message TEXT;
```

### ⏳ 迁移后的SQL（未来）
```sql
-- 迁移完成后可以使用的SQL
UPDATE roomserver_expiring_messages 
SET status = 'processing', updated_at = NOW()
WHERE event_id IN (
    SELECT event_id 
    FROM roomserver_expiring_messages
    WHERE expire_ts <= $1 AND status = 'pending'
    ORDER BY expire_ts ASC
    LIMIT $2
    FOR UPDATE SKIP LOCKED
)
RETURNING event_id, room_id, sender_id, expire_ts
```

## 验证步骤

### 1. 启动Dendrite
```bash
./dendrite
```

应该能正常启动，不再报错。

### 2. 检查迁移是否执行
```sql
-- PostgreSQL
\d roomserver_expiring_messages

-- 应该看到新字段：
-- status | character varying(20) | not null | default 'pending'::character varying
-- retry_count | integer | not null | default 0
-- updated_at | timestamp without time zone | not null | default now()
-- error_message | text |
```

### 3. 测试功能
- 创建阅后即焚消息
- 查看消息是否被正确处理
- 检查日志中的处理信息

## 核心改进保持不变

即使在迁移前，核心的原子查询改进仍然有效：

### ✅ 原子操作
```go
// 一次SQL操作完成查询和标记
expiredMessages, err := s.db.SelectAndMarkExpiredMessages(ctx, currentTS, s.batchSize)
```

### ✅ 高并发支持
- PostgreSQL: `FOR UPDATE SKIP LOCKED`
- SQLite: 条件更新
- 多实例可以并发工作

### ✅ 无锁设计
- 不再需要分布式锁
- 数据库层面保证原子性
- 性能线性扩展

## 下一步计划

### 迁移完成后
1. **验证新字段**：确认`status`等字段已添加
2. **切换SQL**：将SQL语句切换到使用新的状态字段
3. **完整测试**：测试完整的状态流转
4. **性能验证**：确认高并发性能

### 切换到新字段的SQL
```sql
-- 查询时使用status字段
WHERE expire_ts <= $1 AND status = 'pending'

-- 更新时设置具体状态
SET status = 'processing', updated_at = NOW()
SET status = 'completed', updated_at = NOW()
SET status = 'failed', updated_at = NOW(), error_message = $2
```

## 监控建议

### 启动后检查
```bash
# 检查日志中的迁移信息
tail -f dendrite.log | grep -i migration

# 检查是否有错误
tail -f dendrite.log | grep -i error
```

### 数据库检查
```sql
-- 检查表结构
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'roomserver_expiring_messages' 
ORDER BY ordinal_position;

-- 检查索引
SELECT indexname, indexdef 
FROM pg_indexes 
WHERE tablename = 'roomserver_expiring_messages';
```

## 总结

现在的代码状态：
- ✅ **可以正常启动**：使用原有字段，向后兼容
- ✅ **迁移会自动执行**：添加新字段和索引
- ✅ **核心改进有效**：原子查询、高并发、无锁设计
- ⏳ **后续可升级**：迁移完成后切换到完整状态管理

您现在可以安全启动Dendrite了！
