package expiring_messages

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
)

// ExpiringMessageTask 过期消息任务
type ExpiringMessageTask struct {
	EventID   string `json:"event_id"`
	RoomID    string `json:"room_id"`
	SenderID  string `json:"sender_id"`
	ExpireTS  int64  `json:"expire_ts"`
	Retries   int    `json:"retries"`
	MaxRetries int   `json:"max_retries"`
	CreatedAt int64  `json:"created_at"`
}

// RedisQueueManager Redis 队列管理器
type RedisQueueManager struct {
	client       *redis.Client
	queueName    string
	retryQueue   string
	failedQueue  string
	processingQueue string
}

// NewRedisQueueManager 创建 Redis 队列管理器
func NewRedisQueueManager(redisAddr, password string, db int, queuePrefix string) (*RedisQueueManager, error) {
	client := redis.NewClient(&redis.Options{
		Addr:     redisAddr,
		Password: password,
		DB:       db,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("Redis 连接失败: %w", err)
	}

	queueName := fmt.Sprintf("%s:expiring_messages", queuePrefix)
	retryQueue := fmt.Sprintf("%s:expiring_messages:retry", queuePrefix)
	failedQueue := fmt.Sprintf("%s:expiring_messages:failed", queuePrefix)
	processingQueue := fmt.Sprintf("%s:expiring_messages:processing", queuePrefix)

	logrus.WithFields(logrus.Fields{
		"redis_addr":   redisAddr,
		"queue_name":   queueName,
		"retry_queue":  retryQueue,
		"failed_queue": failedQueue,
	}).Info("Redis 队列管理器初始化成功")

	return &RedisQueueManager{
		client:       client,
		queueName:    queueName,
		retryQueue:   retryQueue,
		failedQueue:  failedQueue,
		processingQueue: processingQueue,
	}, nil
}

// PushTask 推送任务到队列
func (q *RedisQueueManager) PushTask(ctx context.Context, task *ExpiringMessageTask) error {
	taskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("序列化任务失败: %w", err)
	}

	// 使用 LPUSH 推送到队列左端
	err = q.client.LPush(ctx, q.queueName, taskData).Err()
	if err != nil {
		return fmt.Errorf("推送任务到队列失败: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"event_id": task.EventID,
		"queue":    q.queueName,
	}).Debug("任务已推送到队列")

	return nil
}

// PopTask 从队列弹出任务（阻塞式）
func (q *RedisQueueManager) PopTask(ctx context.Context, timeout time.Duration) (*ExpiringMessageTask, error) {
	// 使用 BRPOPLPUSH 原子性地从主队列弹出并推送到处理队列
	result, err := q.client.BRPopLPush(ctx, q.queueName, q.processingQueue, timeout).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil // 超时，没有任务
		}
		return nil, fmt.Errorf("弹出任务失败: %w", err)
	}

	var task ExpiringMessageTask
	if err := json.Unmarshal([]byte(result), &task); err != nil {
		// 如果反序列化失败，将任务移到失败队列
		q.client.LPush(ctx, q.failedQueue, result)
		q.client.LRem(ctx, q.processingQueue, 1, result)
		return nil, fmt.Errorf("反序列化任务失败: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"event_id": task.EventID,
		"queue":    q.queueName,
	}).Debug("从队列弹出任务")

	return &task, nil
}

// CompleteTask 完成任务（从处理队列中移除）
func (q *RedisQueueManager) CompleteTask(ctx context.Context, task *ExpiringMessageTask) error {
	taskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("序列化任务失败: %w", err)
	}

	// 从处理队列中移除任务
	removed := q.client.LRem(ctx, q.processingQueue, 1, string(taskData)).Val()
	if removed == 0 {
		logrus.WithField("event_id", task.EventID).Warn("任务不在处理队列中")
	}

	logrus.WithField("event_id", task.EventID).Debug("任务处理完成")
	return nil
}

// RetryTask 重试任务
func (q *RedisQueueManager) RetryTask(ctx context.Context, task *ExpiringMessageTask) error {
	task.Retries++
	
	taskData, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("序列化任务失败: %w", err)
	}

	// 从处理队列中移除原任务
	q.client.LRem(ctx, q.processingQueue, 1, string(taskData))

	if task.Retries >= task.MaxRetries {
		// 超过最大重试次数，移到失败队列
		err = q.client.LPush(ctx, q.failedQueue, taskData).Err()
		if err != nil {
			return fmt.Errorf("推送任务到失败队列失败: %w", err)
		}
		logrus.WithFields(logrus.Fields{
			"event_id": task.EventID,
			"retries":  task.Retries,
		}).Warn("任务超过最大重试次数，移到失败队列")
	} else {
		// 推送到重试队列
		err = q.client.LPush(ctx, q.retryQueue, taskData).Err()
		if err != nil {
			return fmt.Errorf("推送任务到重试队列失败: %w", err)
		}
		logrus.WithFields(logrus.Fields{
			"event_id": task.EventID,
			"retries":  task.Retries,
		}).Debug("任务已推送到重试队列")
	}

	return nil
}

// GetQueueStats 获取队列统计信息
func (q *RedisQueueManager) GetQueueStats(ctx context.Context) (map[string]int64, error) {
	pipe := q.client.Pipeline()
	
	mainLen := pipe.LLen(ctx, q.queueName)
	retryLen := pipe.LLen(ctx, q.retryQueue)
	failedLen := pipe.LLen(ctx, q.failedQueue)
	processingLen := pipe.LLen(ctx, q.processingQueue)
	
	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取队列统计失败: %w", err)
	}

	return map[string]int64{
		"main":       mainLen.Val(),
		"retry":      retryLen.Val(),
		"failed":     failedLen.Val(),
		"processing": processingLen.Val(),
	}, nil
}

// ProcessRetryQueue 处理重试队列（定期将重试队列中的任务移回主队列）
func (q *RedisQueueManager) ProcessRetryQueue(ctx context.Context) error {
	// 使用 RPOPLPUSH 将重试队列中的任务移到主队列
	for {
		result, err := q.client.RPopLPush(ctx, q.retryQueue, q.queueName).Result()
		if err == redis.Nil {
			break // 重试队列为空
		}
		if err != nil {
			return fmt.Errorf("处理重试队列失败: %w", err)
		}
		
		logrus.Debug("将任务从重试队列移回主队列")
		_ = result // 避免未使用变量警告
	}
	
	return nil
}

// RecoverProcessingTasks 恢复处理队列中的任务（用于服务重启后恢复）
func (q *RedisQueueManager) RecoverProcessingTasks(ctx context.Context) error {
	// 将处理队列中的所有任务移回主队列
	for {
		result, err := q.client.RPopLPush(ctx, q.processingQueue, q.queueName).Result()
		if err == redis.Nil {
			break // 处理队列为空
		}
		if err != nil {
			return fmt.Errorf("恢复处理队列失败: %w", err)
		}
		
		logrus.Debug("将任务从处理队列移回主队列")
		_ = result
	}
	
	return nil
}

// Close 关闭队列管理器
func (q *RedisQueueManager) Close() error {
	return q.client.Close()
}
