package expiring_messages

import (
	"crypto/ed25519"
	"time"

	"github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/roomserver/storage"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/matrix-org/gomatrixserverlib"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/sirupsen/logrus"
)

// IntegrateWithRoomserver 展示如何将过期消息服务集成到 Roomserver 中
// func IntegrateWithRoomserver(db storage.Database, rsAPI api.RoomserverInternalAPI) *ExpiringMessagesService {
// 	// 1. 创建过期消息服务（使用默认配置，但没有签名信息）
// 	// 注意：这种方式创建的服务无法发送撤回事件
// 	service := NewExpiringMessagesServiceWithDefaults(db, rsAPI)

// 	// 2. 启动服务（在 goroutine 中运行）
// 	go func() {
// 		logrus.Info("启动阅后即焚消息服务...")
// 		service.Start()
// 	}()

// 	// 3. 返回服务实例，以便其他组件可以使用
// 	return service
// }

// IntegrateWithConfig 推荐的集成方式：从完整配置创建服务
func IntegrateWithConfig(
	db storage.Database,
	rsAPI api.RoomserverInternalAPI,
	cfg *config.Dendrite,
) (*ExpiringMessagesService, error) {
	// 1. 从配置创建服务（包含完整的签名信息）
	service, err := CreateServiceFromConfig(db, rsAPI, cfg)
	if err != nil {
		return nil, err
	}

	// 2. 启动服务（在 goroutine 中运行）
	go func() {
		logrus.Info("启动阅后即焚消息服务...")
		service.Start()
	}()

	// 3. 返回服务实例
	return service, nil
}

// IntegrateWithManualConfig 手动指定配置的集成方式
func IntegrateWithManualConfig(
	db storage.Database,
	rsAPI api.RoomserverInternalAPI,
	serverName spec.ServerName,
	keyID gomatrixserverlib.KeyID,
	privateKey ed25519.PrivateKey,
) (*ExpiringMessagesService, error) {
	// 1. 验证签名配置
	if err := ValidateSigningConfig(serverName, keyID, privateKey); err != nil {
		return nil, err
	}

	// 2. 获取默认配置
	config := GetDefaultConfig()

	// 3. 创建服务
	service := CreateServiceWithManualConfig(db, rsAPI, serverName, keyID, privateKey, config)

	// 4. 启动服务
	go func() {
		logrus.Info("启动阅后即焚消息服务...")
		service.Start()
	}()

	return service, nil
}

// ExampleUsage 展示如何使用过期消息功能
func ExampleUsage() {
	// 这是一个示例，展示客户端如何发送阅后即焚消息

	// 客户端发送消息时的 JSON 内容示例：
	messageContent := map[string]interface{}{
		"msgtype":   "m.text",
		"body":      "这是一条阅后即焚消息，将在1小时后自动销毁",
		"expire_ts": time.Now().Add(time.Hour).UnixMilli(), // 1小时后过期
	}

	logrus.WithField("content", messageContent).Info("客户端发送阅后即焚消息示例")

	// 服务端处理流程：
	// 1. 消息通过 ClientAPI 发送到 Roomserver
	// 2. Roomserver 在 input_events.go 中的 handleExpiringMessage 检测到 expire_ts 字段
	// 3. 将消息信息存储到 roomserver_expiring_messages 表
	// 4. 过期消息服务定期检查并自动撤回过期消息
}

// ConfigurationExample 展示配置示例
func ConfigurationExample() {
	// 在 dendrite.yaml 中可以添加以下配置（可选）：
	configExample := `
roomserver:
  expiring_messages:
    # 检查过期消息的间隔（默认30秒）
    check_interval: "30s"
    
    # 每次处理的最大消息数量（默认100）
    batch_size: 100
    
    # 保留已处理记录的天数（默认7天）
    cleanup_days: 7
    
    # 最大过期时间限制（默认30天）
    max_expire_days: 30
`

	logrus.WithField("config", configExample).Info("阅后即焚消息服务配置示例")
}

// MonitoringExample 展示监控和日志示例
func MonitoringExample() {
	// 关键监控指标：
	metrics := map[string]string{
		"expiring_messages_processed_total":     "已处理的过期消息总数",
		"expiring_messages_processing_errors":   "处理过期消息时的错误数",
		"expiring_messages_queue_size":          "待处理的过期消息队列大小",
		"expiring_messages_processing_duration": "处理过期消息的平均耗时",
	}

	// 关键日志事件：
	logEvents := map[string]string{
		"INFO: 启动阅后即焚消息处理服务":         "服务启动",
		"INFO: 发现过期消息，开始处理 count=X":  "发现过期消息",
		"INFO: 过期消息处理完成 event_id=X":  "单个消息处理完成",
		"ERROR: 处理过期消息失败 event_id=X": "处理失败",
		"ERROR: 查询过期消息失败":            "数据库查询失败",
	}

	logrus.WithFields(logrus.Fields{
		"metrics":    metrics,
		"log_events": logEvents,
	}).Info("阅后即焚消息服务监控示例")
}

// TroubleshootingGuide 故障排除指南
func TroubleshootingGuide() {
	troubleshooting := map[string]string{
		"消息没有自动撤回": `
1. 检查过期消息服务是否启动：grep "启动阅后即焚" /var/log/dendrite.log
2. 检查 expire_ts 格式是否正确（毫秒时间戳）
3. 查看是否有错误日志：grep "处理过期消息失败" /var/log/dendrite.log
4. 手动查询数据库：SELECT * FROM roomserver_expiring_messages WHERE processed = 0;`,

		"数据库性能问题": `
1. 添加索引：CREATE INDEX IF NOT EXISTS idx_expire_ts ON roomserver_expiring_messages (expire_ts) WHERE processed = 0;
2. 定期清理：DELETE FROM roomserver_expiring_messages WHERE processed = 1 AND created_at < NOW() - INTERVAL '30 days';
3. 监控查询耗时`,

		"撤回事件发送失败": `
1. 检查发送者权限
2. 确认房间仍然存在
3. 检查 Roomserver API 状态
4. 查看网络连接`,
	}

	logrus.WithField("troubleshooting", troubleshooting).Info("阅后即焚消息服务故障排除指南")
}

// PerformanceOptimization 性能优化建议
func PerformanceOptimization() {
	optimizations := map[string]string{
		"数据库优化": `
1. 使用复合索引：(processed, expire_ts)
2. 分区表（如果消息量很大）
3. 定期清理已处理的记录
4. 使用连接池`,

		"服务优化": `
1. 调整检查间隔（根据消息量）
2. 批量处理过期消息
3. 并发处理（注意并发控制）
4. 添加熔断机制`,

		"监控优化": `
1. 添加 Prometheus 指标
2. 设置告警规则
3. 监控处理延迟
4. 跟踪错误率`,
	}

	logrus.WithField("optimizations", optimizations).Info("阅后即焚消息服务性能优化建议")
}

// SecurityConsiderations 安全考虑
func SecurityConsiderations() {
	security := map[string]string{
		"输入验证": `
1. 验证 expire_ts 的合理性（不能是过去的时间）
2. 限制最长过期时间（如30天）
3. 验证用户权限（只有发送者可以设置过期时间）`,

		"审计日志": `
1. 记录所有撤回操作
2. 包含用户ID、房间ID、事件ID
3. 记录撤回原因和时间戳`,

		"数据保护": `
1. 确保已撤回的消息内容被完全删除
2. 防止通过备份恢复已撤回的消息
3. 考虑端到端加密的影响`,
	}

	logrus.WithField("security", security).Info("阅后即焚消息服务安全考虑")
}
