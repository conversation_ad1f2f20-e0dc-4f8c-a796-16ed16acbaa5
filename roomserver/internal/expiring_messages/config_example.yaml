# 阅后即焚消息配置示例

# 传统模式配置（向后兼容）
expiring_messages_legacy:
  enabled: true
  check_interval: 30s
  batch_size: 100
  cleanup_days: 7
  max_expire_days: 30
  redis:
    enabled: true
    address: "localhost:6379"
    password: ""
    database: 0

# 队列模式配置（推荐用于生产环境）
expiring_messages_queue:
  enabled: true
  check_interval: 10s  # 生产者扫描间隔
  batch_size: 50       # 每次扫描的批量大小
  cleanup_days: 7
  max_expire_days: 30
  
  # 队列模式配置
  use_queue: true
  is_producer: true    # 是否启用生产者
  is_consumer: true    # 是否启用消费者
  consumer_count: 3    # 消费者数量
  max_retries: 3       # 最大重试次数
  
  redis:
    enabled: true
    address: "localhost:6379"
    password: ""
    database: 0

# 多机部署示例

# 机器1：专门的生产者
machine1_producer_only:
  enabled: true
  check_interval: 5s   # 更频繁的扫描
  batch_size: 100
  cleanup_days: 7
  max_expire_days: 30
  
  use_queue: true
  is_producer: true    # 只作为生产者
  is_consumer: false   # 不消费
  consumer_count: 0
  max_retries: 3
  
  redis:
    enabled: true
    address: "redis-cluster:6379"
    password: "your_redis_password"
    database: 0

# 机器2-4：专门的消费者
machine2_4_consumer_only:
  enabled: true
  check_interval: 30s  # 消费者不需要频繁扫描
  batch_size: 50
  cleanup_days: 7
  max_expire_days: 30
  
  use_queue: true
  is_producer: false   # 不生产
  is_consumer: true    # 只消费
  consumer_count: 5    # 每台机器5个消费者
  max_retries: 3
  
  redis:
    enabled: true
    address: "redis-cluster:6379"
    password: "your_redis_password"
    database: 0

# 机器5：混合模式（生产+消费）
machine5_hybrid:
  enabled: true
  check_interval: 15s
  batch_size: 50
  cleanup_days: 7
  max_expire_days: 30
  
  use_queue: true
  is_producer: true    # 既生产
  is_consumer: true    # 也消费
  consumer_count: 2    # 较少的消费者
  max_retries: 3
  
  redis:
    enabled: true
    address: "redis-cluster:6379"
    password: "your_redis_password"
    database: 0

# 高可用Redis集群配置
redis_cluster_config:
  enabled: true
  # 使用Redis Sentinel或Cluster
  addresses:
    - "redis-1:6379"
    - "redis-2:6379" 
    - "redis-3:6379"
  password: "your_redis_password"
  database: 0
  # 连接池配置
  pool_size: 10
  min_idle_conns: 5
  max_retries: 3
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s

# 监控和告警配置
monitoring:
  # 队列长度告警阈值
  queue_length_warning: 1000
  queue_length_critical: 5000
  
  # 处理延迟告警阈值
  processing_delay_warning: 300s  # 5分钟
  processing_delay_critical: 900s # 15分钟
  
  # 失败率告警阈值
  failure_rate_warning: 0.05     # 5%
  failure_rate_critical: 0.10    # 10%

# 性能调优建议
performance_tuning:
  # 生产者配置
  producer:
    # 扫描间隔：根据消息量调整
    # - 高消息量：5-10秒
    # - 中等消息量：10-30秒
    # - 低消息量：30-60秒
    check_interval: "10s"
    
    # 批量大小：根据数据库性能调整
    # - 高性能数据库：100-500
    # - 中等性能数据库：50-100
    # - 低性能数据库：10-50
    batch_size: 100
  
  # 消费者配置
  consumer:
    # 消费者数量：根据CPU核心数调整
    # - 建议：CPU核心数 * 1.5 - 2
    # - 最小：1个消费者
    # - 最大：不超过CPU核心数 * 3
    consumer_count: 3
    
    # 重试配置
    max_retries: 3
    retry_delay: "5s"
  
  # Redis配置
  redis:
    # 连接池大小：根据消费者数量调整
    # - 建议：消费者数量 * 2
    pool_size: 10
    
    # 超时配置
    dial_timeout: "5s"
    read_timeout: "3s"
    write_timeout: "3s"

# 故障恢复配置
disaster_recovery:
  # 启动时恢复处理队列中的任务
  recover_on_startup: true
  
  # 定期处理重试队列
  retry_queue_interval: "60s"
  
  # 失败队列处理
  failed_queue_cleanup_interval: "24h"
  failed_queue_retention_days: 7

# 日志配置
logging:
  level: "info"  # debug, info, warn, error
  
  # 详细日志（用于调试）
  detailed_logging:
    enabled: false
    log_task_details: true
    log_queue_stats: true
    log_performance_metrics: true
