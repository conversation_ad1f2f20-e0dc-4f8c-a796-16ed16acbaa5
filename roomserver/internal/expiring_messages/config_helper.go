package expiring_messages

import (
	"crypto/ed25519"
	"fmt"
	"time"

	"github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/roomserver/storage"
	"github.com/element-hq/dendrite/setup/config"
	"github.com/matrix-org/gomatrixserverlib"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/sirupsen/logrus"
)

// CreateServiceFromConfig 从 Dendrite 配置创建过期消息服务
func CreateServiceFromConfig(
	db storage.Database,
	rsAPI api.RoomserverInternalAPI,
	cfg *config.Dendrite,
) (*ExpiringMessagesService, error) {
	// 获取签名信息
	serverName := cfg.Global.ServerName
	keyID := cfg.Global.KeyID
	privateKey := cfg.Global.PrivateKey

	// 验证签名信息
	if serverName == "" {
		return nil, fmt.Errorf("服务器名称未配置")
	}
	if keyID == "" {
		return nil, fmt.Errorf("密钥ID未配置")
	}
	if privateKey == nil {
		return nil, fmt.Errorf("私钥未配置")
	}
	fmt.Printf("过期消息配置  %+v\n", cfg.RoomServer)

	// 获取过期消息配置
	expiringConfig := ExpiringMessagesConfig{
		CheckInterval: cfg.RoomServer.ExpiringMessages.CheckInterval,
		BatchSize:     cfg.RoomServer.ExpiringMessages.BatchSize,
		CleanupDays:   cfg.RoomServer.ExpiringMessages.CleanupDays,
		MaxExpireDays: cfg.RoomServer.ExpiringMessages.MaxExpireDays,
		Enabled:       cfg.RoomServer.ExpiringMessages.Enabled,

		// 队列模式配置（如果配置文件中没有，使用默认值）
		UseQueue:      false, // 默认使用传统模式
		IsProducer:    true,  // 默认既是生产者
		IsConsumer:    true,  // 也是消费者
		ConsumerCount: 2,     // 默认2个消费者
		MaxRetries:    3,     // 默认最大重试3次
	}

	// 如果配置为空，使用默认值
	if expiringConfig.CheckInterval == 0 {
		expiringConfig.CheckInterval = 30 * time.Second
	}
	if expiringConfig.BatchSize == 0 {
		expiringConfig.BatchSize = 100
	}
	if expiringConfig.CleanupDays == 0 {
		expiringConfig.CleanupDays = 7
	}
	if expiringConfig.MaxExpireDays == 0 {
		expiringConfig.MaxExpireDays = 30
	}

	// 创建服务
	service := NewExpiringMessagesService(
		db, rsAPI, expiringConfig,
		serverName, keyID, privateKey,
	)

	// 如果启用了 Redis 分布式锁，初始化 Redis 锁管理器
	if cfg.RoomServer.ExpiringMessages.Redis.Enabled {
		lockManager, err := NewRedisLockManager(
			cfg.RoomServer.ExpiringMessages.Redis.Address,
			cfg.RoomServer.ExpiringMessages.Redis.Password,
			cfg.RoomServer.ExpiringMessages.Redis.Database,
		)
		if err != nil {
			logrus.WithError(err).Error("初始化 Redis 锁管理器失败，将使用单机模式")
		} else {
			service.SetRedisLockManager(lockManager)
			logrus.Info("Redis 分布式锁已启用")
		}

		// 如果启用了队列模式，初始化 Redis 队列管理器
		if expiringConfig.UseQueue {
			queueManager, err := NewRedisQueueManager(
				cfg.RoomServer.ExpiringMessages.Redis.Address,
				cfg.RoomServer.ExpiringMessages.Redis.Password,
				cfg.RoomServer.ExpiringMessages.Redis.Database,
				string(serverName), // 使用服务器名作为队列前缀
			)
			if err != nil {
				logrus.WithError(err).Error("初始化 Redis 队列管理器失败，将使用传统模式")
			} else {
				service.SetRedisQueueManager(queueManager)
				logrus.WithFields(logrus.Fields{
					"is_producer":    expiringConfig.IsProducer,
					"is_consumer":    expiringConfig.IsConsumer,
					"consumer_count": expiringConfig.ConsumerCount,
				}).Info("Redis 队列管理器已启用")
			}
		}
	}

	logrus.WithFields(logrus.Fields{
		"server_name":     serverName,
		"key_id":          keyID,
		"check_interval":  expiringConfig.CheckInterval,
		"batch_size":      expiringConfig.BatchSize,
		"cleanup_days":    expiringConfig.CleanupDays,
		"max_expire_days": expiringConfig.MaxExpireDays,
		"enabled":         expiringConfig.Enabled,
		"redis_enabled":   cfg.RoomServer.ExpiringMessages.Redis.Enabled,
	}).Info("创建阅后即焚消息服务")

	return service, nil
}

// CreateServiceWithManualConfig 手动指定配置创建服务
func CreateServiceWithManualConfig(
	db storage.Database,
	rsAPI api.RoomserverInternalAPI,
	serverName spec.ServerName,
	keyID gomatrixserverlib.KeyID,
	privateKey ed25519.PrivateKey,
	config ExpiringMessagesConfig,
) *ExpiringMessagesService {
	return NewExpiringMessagesService(
		db, rsAPI, config,
		serverName, keyID, privateKey,
	)
}

// ValidateSigningConfig 验证签名配置是否完整
func ValidateSigningConfig(serverName spec.ServerName, keyID gomatrixserverlib.KeyID, privateKey ed25519.PrivateKey) error {
	if serverName == "" {
		return fmt.Errorf("服务器名称不能为空")
	}
	if keyID == "" {
		return fmt.Errorf("密钥ID不能为空")
	}
	if privateKey == nil {
		return fmt.Errorf("私钥不能为空")
	}
	if len(privateKey) != ed25519.PrivateKeySize {
		return fmt.Errorf("私钥长度不正确，期望 %d 字节，实际 %d 字节",
			ed25519.PrivateKeySize, len(privateKey))
	}
	return nil
}

// GetDefaultConfig 获取默认的过期消息配置
func GetDefaultConfig() ExpiringMessagesConfig {
	return ExpiringMessagesConfig{
		CheckInterval: 30 * time.Second,
		BatchSize:     100,
		CleanupDays:   7,
		MaxExpireDays: 30,
		Enabled:       true,
	}
}

// LogConfigInfo 记录配置信息
func LogConfigInfo(config ExpiringMessagesConfig, serverName spec.ServerName, keyID gomatrixserverlib.KeyID) {
	logrus.WithFields(logrus.Fields{
		"server_name":     serverName,
		"key_id":          keyID,
		"check_interval":  config.CheckInterval,
		"batch_size":      config.BatchSize,
		"cleanup_days":    config.CleanupDays,
		"max_expire_days": config.MaxExpireDays,
		"enabled":         config.Enabled,
	}).Info("阅后即焚消息服务配置")
}
