package expiring_messages

import (
	"context"
	"testing"
	"time"

	"github.com/element-hq/dendrite/roomserver/storage/shared"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockDatabase 模拟数据库接口
type MockDatabase struct {
	mock.Mock
}

func (m *MockDatabase) InsertExpiringMessage(ctx context.Context, eventID, roomID, senderID string, expireTS int64) error {
	args := m.Called(ctx, eventID, roomID, senderID, expireTS)
	return args.Error(0)
}

func (m *MockDatabase) SelectExpiredMessages(ctx context.Context, currentTS int64, limit int) ([]shared.ExpiringMessage, error) {
	args := m.Called(ctx, currentTS, limit)
	return args.Get(0).([]shared.ExpiringMessage), args.Error(1)
}

func (m *MockDatabase) MarkMessageProcessed(ctx context.Context, eventID string) error {
	args := m.Called(ctx, eventID)
	return args.Error(0)
}

func (m *MockDatabase) DeleteProcessedMessages(ctx context.Context, beforeTime time.Time) error {
	args := m.Called(ctx, beforeTime)
	return args.Error(0)
}

func (m *MockDatabase) SelectExpiringMessage(ctx context.Context, eventID string) (*shared.ExpiringMessage, error) {
	args := m.Called(ctx, eventID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*shared.ExpiringMessage), args.Error(1)
}

// MockRoomserverAPI 模拟房间服务器API
type MockRoomserverAPI struct {
	mock.Mock
}

func (m *MockRoomserverAPI) InputRoomEvents(ctx context.Context, request interface{}, response interface{}) {
	m.Called(ctx, request, response)
}

func TestAddExpiringMessage(t *testing.T) {
	mockDB := &MockDatabase{}
	mockAPI := &MockRoomserverAPI{}

	config := ExpiringMessagesConfig{
		CheckInterval: 30 * time.Second,
		BatchSize:     100,
		CleanupDays:   7,
		MaxExpireDays: 30,
		Enabled:       true,
	}

	service := NewExpiringMessagesService(mockDB, mockAPI, config, "test.server", "test_key", nil)

	ctx := context.Background()
	eventID := "$test_event_id"
	roomID := "!test_room:example.com"
	senderID := "@test_user:example.com"
	expireTS := time.Now().Add(time.Hour).UnixMilli()

	// 设置期望调用
	mockDB.On("InsertExpiringMessage", ctx, eventID, roomID, senderID, expireTS).Return(nil)

	// 执行测试
	err := service.AddExpiringMessage(ctx, eventID, roomID, senderID, expireTS)

	// 验证结果
	assert.NoError(t, err)
	mockDB.AssertExpectations(t)
}

func TestProcessExpiredMessages(t *testing.T) {
	mockDB := &MockDatabase{}
	mockAPI := &MockRoomserverAPI{}

	config := ExpiringMessagesConfig{
		CheckInterval: 30 * time.Second,
		BatchSize:     100,
		CleanupDays:   7,
		MaxExpireDays: 30,
		Enabled:       true,
	}

	service := NewExpiringMessagesService(mockDB, mockAPI, config, "test.server", "test_key", nil)

	ctx := context.Background()
	currentTS := time.Now().Unix() // 改为秒级时间戳

	// 创建过期消息
	expiredMessage := shared.ExpiringMessage{
		EventID:  "$expired_event",
		RoomID:   "!test_room:example.com",
		SenderID: "@test_user:example.com",
		ExpireTS: currentTS - 1000, // 已过期
	}

	// 设置期望调用
	mockDB.On("SelectExpiredMessages", mock.Anything, mock.AnythingOfType("int64"), 100).
		Return([]shared.ExpiringMessage{expiredMessage}, nil)
	mockDB.On("MarkMessageProcessed", mock.Anything, expiredMessage.EventID).Return(nil)
	mockDB.On("DeleteProcessedMessages", mock.Anything, mock.AnythingOfType("time.Time")).Return(nil)

	// 由于 createRedactionEvent 会失败（没有真实的房间信息），我们期望处理失败
	// 但服务应该继续运行而不崩溃

	// 执行测试
	service.processExpiredMessages()

	// 验证数据库调用
	mockDB.AssertCalled(t, "SelectExpiredMessages", mock.Anything, mock.AnythingOfType("int64"), 100)
	mockDB.AssertCalled(t, "DeleteProcessedMessages", mock.Anything, mock.AnythingOfType("time.Time"))
}

func TestServiceStartStop(t *testing.T) {
	mockDB := &MockDatabase{}
	mockAPI := &MockRoomserverAPI{}

	config := ExpiringMessagesConfig{
		CheckInterval: 30 * time.Second,
		BatchSize:     100,
		CleanupDays:   7,
		MaxExpireDays: 30,
		Enabled:       true,
	}

	service := NewExpiringMessagesService(mockDB, mockAPI, config, "test.server", "test_key", nil)

	// 测试服务启动和停止
	go service.Start()

	// 等待一小段时间确保服务启动
	time.Sleep(100 * time.Millisecond)

	// 停止服务
	service.Stop()

	// 验证服务正常停止（没有panic）
	assert.True(t, true)
}
