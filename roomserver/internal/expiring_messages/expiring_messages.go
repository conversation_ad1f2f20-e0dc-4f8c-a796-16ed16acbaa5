package expiring_messages

import (
	"context"
	"crypto/ed25519"
	"fmt"
	"net"
	"os"
	"time"

	"github.com/element-hq/dendrite/internal/eventutil"
	"github.com/element-hq/dendrite/roomserver/api"
	"github.com/element-hq/dendrite/roomserver/storage"
	"github.com/element-hq/dendrite/roomserver/storage/shared"
	"github.com/element-hq/dendrite/roomserver/types"
	"github.com/google/uuid"
	"github.com/matrix-org/gomatrixserverlib"
	"github.com/matrix-org/gomatrixserverlib/fclient"
	"github.com/matrix-org/gomatrixserverlib/spec"
	"github.com/sirupsen/logrus"
)

// ExpiringMessagesService 处理阅后即焚消息的服务
type ExpiringMessagesService struct {
	db         storage.Database
	rsAPI      api.RoomserverInternalAPI
	stopChan   chan struct{}
	serverName spec.ServerName
	keyID      gomatrixserverlib.KeyID
	privateKey ed25519.PrivateKey

	// 配置参数
	checkInterval time.Duration
	batchSize     int
	cleanupDays   int
	maxExpireDays int
	enabled       bool

	// Redis 分布式锁和队列
	lockManager  *RedisLockManager
	queueManager *RedisQueueManager
	instanceID   string

	// 生产者和消费者控制
	isProducer    bool
	isConsumer    bool
	consumerCount int
}

// ExpiringMessagesConfig 过期消息配置
type ExpiringMessagesConfig struct {
	CheckInterval time.Duration
	BatchSize     int
	CleanupDays   int
	MaxExpireDays int
	Enabled       bool

	// 队列模式配置
	UseQueue      bool
	IsProducer    bool
	IsConsumer    bool
	ConsumerCount int
	MaxRetries    int
}

// NewExpiringMessagesService 创建新的过期消息服务
func NewExpiringMessagesService(
	db storage.Database,
	rsAPI api.RoomserverInternalAPI,
	config ExpiringMessagesConfig,
	serverName spec.ServerName,
	keyID gomatrixserverlib.KeyID,
	privateKey ed25519.PrivateKey,
) *ExpiringMessagesService {
	// 生成更唯一的实例ID：IP+主机名+PID+UUID
	instanceID := generateUniqueInstanceID(string(serverName))

	return &ExpiringMessagesService{
		db:            db,
		rsAPI:         rsAPI,
		stopChan:      make(chan struct{}),
		serverName:    serverName,
		keyID:         keyID,
		privateKey:    privateKey,
		checkInterval: config.CheckInterval,
		batchSize:     config.BatchSize,
		cleanupDays:   config.CleanupDays,
		maxExpireDays: config.MaxExpireDays,
		enabled:       config.Enabled,
		instanceID:    instanceID,
		lockManager:   nil, // 将在 SetRedisLockManager 中设置
		queueManager:  nil, // 将在 SetRedisQueueManager 中设置
		isProducer:    config.IsProducer,
		isConsumer:    config.IsConsumer,
		consumerCount: config.ConsumerCount,
	}
}

// SetRedisLockManager 设置 Redis 锁管理器
func (s *ExpiringMessagesService) SetRedisLockManager(lockManager *RedisLockManager) {
	s.lockManager = lockManager
	logrus.WithField("instance_id", s.instanceID).Info("Redis 分布式锁已启用")
}

// SetRedisQueueManager 设置 Redis 队列管理器
func (s *ExpiringMessagesService) SetRedisQueueManager(queueManager *RedisQueueManager) {
	s.queueManager = queueManager
	logrus.WithField("instance_id", s.instanceID).Info("Redis 队列管理器已启用")
}

// NewExpiringMessagesServiceWithDefaults 使用默认配置创建过期消息服务
// 注意：这个函数不包含签名信息，createRedactionEvent 会失败
// 建议使用 NewExpiringMessagesService 并传入完整的签名信息
func NewExpiringMessagesServiceWithDefaults(db storage.Database, rsAPI api.RoomserverInternalAPI) *ExpiringMessagesService {
	defaultConfig := ExpiringMessagesConfig{
		CheckInterval: 30 * time.Second,
		BatchSize:     100,
		CleanupDays:   7,
		MaxExpireDays: 30,
		Enabled:       true,
	}
	// 使用空的签名信息，实际使用时需要提供真实的签名信息
	return NewExpiringMessagesService(
		db, rsAPI, defaultConfig,
		"", "", nil,
	)
}

// Start 启动过期消息处理服务
func (s *ExpiringMessagesService) Start() {
	if !s.enabled {
		logrus.Info("阅后即焚消息功能已禁用，跳过启动")
		return
	}

	logrus.WithFields(logrus.Fields{
		"check_interval":  s.checkInterval,
		"batch_size":      s.batchSize,
		"cleanup_days":    s.cleanupDays,
		"max_expire_days": s.maxExpireDays,
		"is_producer":     s.isProducer,
		"is_consumer":     s.isConsumer,
		"consumer_count":  s.consumerCount,
		"instance_id":     s.instanceID,
	}).Info("启动阅后即焚消息处理服务")

	// 如果启用了队列模式
	if s.queueManager != nil {
		s.startQueueMode()
	} else {
		s.startLegacyMode()
	}
}

// startQueueMode 启动基于队列的模式
func (s *ExpiringMessagesService) startQueueMode() {
	// 启动生产者
	if s.isProducer {
		go s.runProducer()
	}

	// 启动消费者
	if s.isConsumer {
		for i := 0; i < s.consumerCount; i++ {
			go s.runConsumer(i)
		}
	}

	// 等待停止信号
	<-s.stopChan
	logrus.Info("停止阅后即焚消息处理服务")
}

// startLegacyMode 启动传统模式（向后兼容）
func (s *ExpiringMessagesService) startLegacyMode() {
	// 使用配置的检查间隔
	ticker := time.NewTicker(s.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.processExpiredMessages()
		case <-s.stopChan:
			logrus.Info("停止阅后即焚消息处理服务")
			return
		}
	}
}

// Stop 停止过期消息处理服务
func (s *ExpiringMessagesService) Stop() {
	close(s.stopChan)
}

// processExpiredMessages 处理已过期的消息（支持多机部署）
func (s *ExpiringMessagesService) processExpiredMessages() {
	ctx := context.Background()
	currentTS := time.Now().Unix() // 改为秒级时间戳

	// 🔒 使用分布式锁防止多机重复处理
	lockKey := "expiring_messages_processing"
	lock, acquired := s.acquireDistributedLock(ctx, lockKey)
	if !acquired {
		logrus.Debug("其他实例正在处理过期消息，跳过本次处理")
		return
	}
	defer s.releaseDistributedLock(ctx, lock)

	// 查询已过期的消息，使用配置的批量大小
	expiredMessages, err := s.db.SelectExpiredMessages(ctx, currentTS, s.batchSize)
	if err != nil {
		logrus.WithError(err).Error("查询过期消息失败")
		return
	}

	if len(expiredMessages) == 0 {
		return
	}

	logrus.WithFields(logrus.Fields{
		"count":     len(expiredMessages),
		"server_id": s.getServerInstanceID(),
	}).Info("发现过期消息，开始处理")

	// 🔄 逐个处理消息，每个消息都加锁
	for _, msg := range expiredMessages {
		if s.processExpiredMessageWithLock(ctx, msg) {
			// 处理成功，继续下一个
		} else {
			// 处理失败或被其他实例处理，跳过
			logrus.WithField("event_id", msg.EventID).Debug("消息已被其他实例处理或处理失败")
		}
	}

	// 清理已处理的旧记录
	s.cleanupOldRecords(ctx)
}

// processExpiredMessage 处理单个过期消息
func (s *ExpiringMessagesService) processExpiredMessage(ctx context.Context, msg shared.ExpiringMessage) error {
	logrus.WithFields(logrus.Fields{
		"event_id":  msg.EventID,
		"room_id":   msg.RoomID,
		"sender_id": msg.SenderID,
		"expire_ts": msg.ExpireTS,
	}).Info("处理过期消息")

	// 创建撤回事件
	redactionEvent, err := s.createRedactionEvent(ctx, msg)
	if err != nil {
		return err
	}

	// 发送撤回事件到房间
	if err := s.sendRedactionEvent(ctx, msg.RoomID, redactionEvent); err != nil {
		return err
	}

	// 标记消息为已处理
	if err := s.db.MarkMessageProcessed(ctx, msg.EventID); err != nil {
		logrus.WithError(err).WithField("event_id", msg.EventID).Error("标记消息为已处理失败")
		return err
	}

	logrus.WithField("event_id", msg.EventID).Info("过期消息处理完成")
	return nil
}

// createRedactionEvent 创建撤回事件
func (s *ExpiringMessagesService) createRedactionEvent(ctx context.Context, msg shared.ExpiringMessage) (*types.HeaderedEvent, error) {
	// 检查必要的参数
	if s.rsAPI == nil {
		return nil, fmt.Errorf("roomserver API 未初始化")
	}

	// 检查签名信息是否可用
	if s.serverName == "" || s.keyID == "" || s.privateKey == nil {
		logrus.WithFields(logrus.Fields{
			"server_name":     s.serverName,
			"key_id":          s.keyID,
			"has_private_key": s.privateKey != nil,
		}).Error("签名信息不完整，无法创建撤回事件")
		return nil, fmt.Errorf("签名信息不完整: serverName=%s, keyID=%s, privateKey=%v",
			s.serverName, s.keyID, s.privateKey != nil)
	}

	// 创建撤回事件内容
	content := map[string]interface{}{
		"reason": "消息已过期（阅后即焚）",
	}

	// 创建 ProtoEvent
	proto := &gomatrixserverlib.ProtoEvent{
		SenderID: msg.SenderID,
		RoomID:   msg.RoomID,
		Type:     "m.room.redaction",
		Redacts:  msg.EventID,
	}

	// 设置内容到 proto
	if err := proto.SetContent(content); err != nil {
		return nil, fmt.Errorf("设置事件内容失败: %w", err)
	}

	// 使用配置的签名信息构建事件
	identity := &fclient.SigningIdentity{
		ServerName: s.serverName,
		KeyID:      s.keyID,
		PrivateKey: s.privateKey,
	}

	logrus.WithFields(logrus.Fields{
		"event_id":    msg.EventID,
		"room_id":     msg.RoomID,
		"sender_id":   msg.SenderID,
		"server_name": s.serverName,
		"key_id":      s.keyID,
	}).Debug("开始构建撤回事件")

	event, err := eventutil.QueryAndBuildEvent(ctx, proto, identity, time.Now(), s.rsAPI, nil)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"event_id":  msg.EventID,
			"room_id":   msg.RoomID,
			"sender_id": msg.SenderID,
		}).Error("构建撤回事件失败")
		return nil, fmt.Errorf("构建撤回事件失败: %w", err)
	}

	logrus.WithField("event_id", msg.EventID).Debug("撤回事件构建成功")
	return event, nil
}

// sendRedactionEvent 发送撤回事件
func (s *ExpiringMessagesService) sendRedactionEvent(ctx context.Context, roomID string, event *types.HeaderedEvent) error {
	// 使用 roomserver API 发送撤回事件
	request := api.InputRoomEventsRequest{
		InputRoomEvents: []api.InputRoomEvent{
			{
				Kind:  api.KindNew,
				Event: event,
			},
		},
		Asynchronous: false,
	}

	var response api.InputRoomEventsResponse
	s.rsAPI.InputRoomEvents(ctx, &request, &response)

	if response.ErrMsg != "" {
		return fmt.Errorf("发送撤回事件失败: %s", response.ErrMsg)
	}

	return nil
}

// cleanupOldRecords 清理旧的已处理记录
func (s *ExpiringMessagesService) cleanupOldRecords(ctx context.Context) {
	// 使用配置的天数删除已处理记录
	beforeTime := time.Now().AddDate(0, 0, -s.cleanupDays)

	if err := s.db.DeleteProcessedMessages(ctx, beforeTime); err != nil {
		logrus.WithError(err).WithField("cleanup_days", s.cleanupDays).Error("清理旧记录失败")
	} else {
		logrus.WithField("cleanup_days", s.cleanupDays).Debug("成功清理旧记录")
	}
}

// AddExpiringMessage 添加需要过期的消息
func (s *ExpiringMessagesService) AddExpiringMessage(ctx context.Context, eventID, roomID, senderID string, expireTS int64) error {
	return s.db.InsertExpiringMessage(ctx, eventID, roomID, senderID, expireTS)
}

// 🔒 Redis 分布式锁相关方法

// acquireDistributedLock 获取分布式锁
func (s *ExpiringMessagesService) acquireDistributedLock(ctx context.Context, lockKey string) (*RedisDistributedLock, bool) {
	if s.lockManager == nil {
		// 如果没有 Redis 锁管理器，回退到单机模式
		logrus.Debug("Redis 锁管理器未配置，使用单机模式")
		return nil, true
	}

	// 使用 Redis 获取分布式锁
	lock, err := s.lockManager.AcquireLock(ctx, lockKey, 5*time.Minute)
	if err != nil {
		logrus.WithError(err).WithField("lock_key", lockKey).Error("获取 Redis 分布式锁失败")
		return nil, false
	}

	if lock.IsAcquired() {
		logrus.WithFields(logrus.Fields{
			"lock_key":    lockKey,
			"instance_id": s.instanceID,
		}).Debug("成功获取分布式锁")
		return lock, true
	}

	logrus.WithFields(logrus.Fields{
		"lock_key":    lockKey,
		"instance_id": s.instanceID,
	}).Debug("锁已被其他实例持有")
	return nil, false
}

// releaseDistributedLock 释放分布式锁
func (s *ExpiringMessagesService) releaseDistributedLock(ctx context.Context, lock *RedisDistributedLock) {
	if lock == nil {
		return
	}

	if err := lock.Release(ctx); err != nil {
		logrus.WithError(err).WithField("lock_key", lock.lockKey).Error("释放分布式锁失败")
	}
}

// getServerInstanceID 获取服务器实例ID
func (s *ExpiringMessagesService) getServerInstanceID() string {
	return s.instanceID
}

// processExpiredMessageWithLock 带锁处理单个过期消息
func (s *ExpiringMessagesService) processExpiredMessageWithLock(ctx context.Context, msg shared.ExpiringMessage) bool {
	// 为每个消息创建独立的锁
	lockKey := fmt.Sprintf("expiring_message_%s", msg.EventID)

	lock, acquired := s.acquireDistributedLock(ctx, lockKey)
	if !acquired {
		logrus.WithField("event_id", msg.EventID).Debug("消息正在被其他实例处理")
		return false
	}
	defer s.releaseDistributedLock(ctx, lock)

	// 再次检查消息是否已被处理（双重检查）
	existingMsg, err := s.db.SelectExpiringMessage(ctx, msg.EventID)
	if err != nil {
		logrus.WithError(err).WithField("event_id", msg.EventID).Error("检查消息状态失败")
		return false
	}

	if existingMsg == nil || existingMsg.Processed {
		logrus.WithField("event_id", msg.EventID).Debug("消息已被其他实例处理")
		return false
	}

	// 处理消息
	err = s.processExpiredMessage(ctx, msg)
	if err != nil {
		logrus.WithError(err).WithField("event_id", msg.EventID).Error("处理过期消息失败")
		return false
	}

	return true
}

// generateUniqueInstanceID 生成唯一的实例ID
func generateUniqueInstanceID(serverName string) string {
	// 获取本机IP地址
	ip := getLocalIP()

	// 获取主机名
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}

	// 生成UUID
	instanceUUID := uuid.New().String()[:8] // 只取前8位

	// 组合：服务器名-IP-主机名-PID-UUID
	return fmt.Sprintf("%s-%s-%s-%d-%s",
		serverName, ip, hostname, os.Getpid(), instanceUUID)
}

// getLocalIP 获取本机IP地址
func getLocalIP() string {
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "unknown"
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String()
			}
		}
	}
	return "unknown"
}

// runProducer 运行生产者（扫描过期消息并推送到队列）
func (s *ExpiringMessagesService) runProducer() {
	logrus.WithField("instance_id", s.instanceID).Info("启动过期消息生产者")

	ticker := time.NewTicker(s.checkInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			s.scanAndPushExpiredMessages()
		case <-s.stopChan:
			logrus.Info("停止过期消息生产者")
			return
		}
	}
}

// runConsumer 运行消费者（从队列消费并处理过期消息）
func (s *ExpiringMessagesService) runConsumer(consumerID int) {
	logrus.WithFields(logrus.Fields{
		"instance_id": s.instanceID,
		"consumer_id": consumerID,
	}).Info("启动过期消息消费者")

	ctx := context.Background()

	for {
		select {
		case <-s.stopChan:
			logrus.WithField("consumer_id", consumerID).Info("停止过期消息消费者")
			return
		default:
			// 从队列弹出任务
			task, err := s.queueManager.PopTask(ctx, 5*time.Second)
			if err != nil {
				logrus.WithError(err).Error("弹出任务失败")
				time.Sleep(time.Second)
				continue
			}

			if task == nil {
				// 没有任务，继续等待
				continue
			}

			// 处理任务
			s.processTask(ctx, task, consumerID)
		}
	}
}

// scanAndPushExpiredMessages 扫描过期消息并推送到队列
func (s *ExpiringMessagesService) scanAndPushExpiredMessages() {
	ctx := context.Background()
	currentTS := time.Now().Unix()

	// 🔒 使用分布式锁防止多个生产者重复扫描
	lockKey := "expiring_messages_scan"
	lock, acquired := s.acquireDistributedLock(ctx, lockKey)
	if !acquired {
		logrus.Debug("其他生产者正在扫描过期消息，跳过本次扫描")
		return
	}
	defer s.releaseDistributedLock(ctx, lock)

	// 查询已过期但未推送的消息
	expiredMessages, err := s.db.SelectExpiredMessages(ctx, currentTS, s.batchSize)
	if err != nil {
		logrus.WithError(err).Error("查询过期消息失败")
		return
	}

	if len(expiredMessages) == 0 {
		return
	}

	logrus.WithFields(logrus.Fields{
		"count":       len(expiredMessages),
		"instance_id": s.instanceID,
	}).Info("发现过期消息，开始推送到队列")

	// 推送到队列
	for _, msg := range expiredMessages {
		// 使用原子操作：查询并标记为已推送
		success, err := s.markMessageAsPushed(ctx, msg.EventID)
		if err != nil {
			logrus.WithError(err).WithField("event_id", msg.EventID).Error("标记消息为已推送失败")
			continue
		}

		if !success {
			// 消息已被其他实例推送，跳过
			logrus.WithField("event_id", msg.EventID).Debug("消息已被其他实例推送")
			continue
		}

		// 创建任务
		task := &ExpiringMessageTask{
			EventID:    msg.EventID,
			RoomID:     msg.RoomID,
			SenderID:   msg.SenderID,
			ExpireTS:   msg.ExpireTS,
			Retries:    0,
			MaxRetries: 3, // 最大重试3次
			CreatedAt:  time.Now().Unix(),
		}

		// 推送到队列
		if err := s.queueManager.PushTask(ctx, task); err != nil {
			logrus.WithError(err).WithField("event_id", msg.EventID).Error("推送任务到队列失败")
			// 推送失败，回滚标记
			s.rollbackMessagePushed(ctx, msg.EventID)
		} else {
			logrus.WithField("event_id", msg.EventID).Debug("任务已推送到队列")
		}
	}
}

// processTask 处理单个任务
func (s *ExpiringMessagesService) processTask(ctx context.Context, task *ExpiringMessageTask, consumerID int) {
	logrus.WithFields(logrus.Fields{
		"event_id":    task.EventID,
		"consumer_id": consumerID,
		"retries":     task.Retries,
	}).Debug("开始处理过期消息任务")

	// 构造 ExpiringMessage 对象
	msg := shared.ExpiringMessage{
		EventID:  task.EventID,
		RoomID:   task.RoomID,
		SenderID: task.SenderID,
		ExpireTS: task.ExpireTS,
	}

	// 处理过期消息
	err := s.processExpiredMessage(ctx, msg)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"event_id":    task.EventID,
			"consumer_id": consumerID,
			"retries":     task.Retries,
		}).Error("处理过期消息任务失败")

		// 重试任务
		if retryErr := s.queueManager.RetryTask(ctx, task); retryErr != nil {
			logrus.WithError(retryErr).WithField("event_id", task.EventID).Error("重试任务失败")
		}
		return
	}

	// 任务处理成功，从处理队列中移除
	if err := s.queueManager.CompleteTask(ctx, task); err != nil {
		logrus.WithError(err).WithField("event_id", task.EventID).Error("完成任务失败")
	}

	logrus.WithFields(logrus.Fields{
		"event_id":    task.EventID,
		"consumer_id": consumerID,
	}).Debug("过期消息任务处理完成")
}

// markMessageAsPushed 原子操作：查询并标记消息为已推送
func (s *ExpiringMessagesService) markMessageAsPushed(ctx context.Context, eventID string) (bool, error) {
	// 使用数据库的原子操作
	return s.db.MarkMessagePushed(ctx, eventID)
}

// rollbackMessagePushed 回滚消息推送标记
func (s *ExpiringMessagesService) rollbackMessagePushed(ctx context.Context, eventID string) {
	if err := s.db.RollbackMessagePushed(ctx, eventID); err != nil {
		logrus.WithError(err).WithField("event_id", eventID).Error("回滚消息推送标记失败")
	}
}
