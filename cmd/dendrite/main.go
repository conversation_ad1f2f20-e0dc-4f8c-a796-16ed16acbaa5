// Copyright 2024 New Vector Ltd.
// Copyright 2017 Vector Creations Ltd
//
// SPDX-License-Identifier: AGPL-3.0-only OR LicenseRef-Element-Commercial
// Please see LICENSE files in the repository root for full details.

// Dendrite 主服务器程序 - 这是 Dendrite Matrix 服务器的主入口点
// 该程序将所有微服务组件组装成一个单体应用程序
// go run .  -really-enable-open-registration
package main

import (
	"flag" // 用于解析命令行参数
	"fmt"
	"time" // 时间相关操作

	// Dendrite 内部包
	"github.com/element-hq/dendrite/internal"          // 内部工具函数和版本信息
	"github.com/element-hq/dendrite/internal/caching"  // 缓存系统
	"github.com/element-hq/dendrite/internal/httputil" // HTTP 路由工具
	"github.com/element-hq/dendrite/internal/sqlutil"  // 数据库连接管理
	"github.com/element-hq/dendrite/setup/jetstream"   // NATS JetStream 消息队列
	"github.com/element-hq/dendrite/setup/process"     // 进程生命周期管理

	// 第三方依赖
	"github.com/getsentry/sentry-go"                  // Sentry 错误监控
	"github.com/matrix-org/gomatrixserverlib/fclient" // Matrix 联邦客户端
	"github.com/prometheus/client_golang/prometheus"  // Prometheus 监控指标
	"github.com/sirupsen/logrus"                      // 日志库

	// Dendrite 微服务组件
	"github.com/element-hq/dendrite/aiservice"          // AI 服务（AI 对话功能）
	"github.com/element-hq/dendrite/appservice"         // 应用服务 API
	"github.com/element-hq/dendrite/federationapi"      // 联邦 API（与其他服务器通信）
	"github.com/element-hq/dendrite/redpacketapi"       // 红包 API（红包功能）
	"github.com/element-hq/dendrite/roomserver"         // 房间服务器（房间状态管理）
	"github.com/element-hq/dendrite/setup"              // 设置和配置
	basepkg "github.com/element-hq/dendrite/setup/base" // 基础设置包（重命名为 basepkg）
	"github.com/element-hq/dendrite/setup/config"       // 配置文件解析
	"github.com/element-hq/dendrite/setup/mscs"         // Matrix 规范变更（MSCs）支持
	"github.com/element-hq/dendrite/userapi"            // 用户 API（用户管理）
)

// 命令行参数定义 - 这些参数可以在启动时通过命令行传递
var (
	// Unix Socket 相关参数（实验性功能）
	unixSocket = flag.String("unix-socket", "",
		"实验性功能：服务器监听的 Unix Socket 路径（会禁用 http[s]-bind-address 功能）",
	)
	unixSocketPermission = flag.String("unix-socket-permission", "755",
		"实验性功能：Unix Socket 的权限设置（使用 chmod 格式，如 755）",
	)

	// HTTP/HTTPS 监听地址
	httpBindAddr  = flag.String("http-bind-address", ":8008", "服务器 HTTP 监听端口")
	httpsBindAddr = flag.String("https-bind-address", ":8448", "服务器 HTTPS 监听端口")

	// TLS 证书文件路径
	certFile = flag.String("tls-cert", "", "用于 TLS 的 PEM 格式 X509 证书文件路径")
	keyFile  = flag.String("tls-key", "", "用于 TLS 的 PEM 格式私钥文件路径")
)

// main 函数 - 程序的主入口点
func main() {
	timeStart := time.Now()
	// 解析配置文件和命令行参数，true 表示这是单体模式
	cfg := setup.ParseFlags(true)

	// 初始化服务器地址配置
	httpAddr := config.ServerAddress{}  // HTTP 服务器地址
	httpsAddr := config.ServerAddress{} // HTTPS 服务器地址

	// 检查是否使用 Unix Socket
	if *unixSocket == "" { // 这个不用管
		// 使用标准的 HTTP/HTTPS 端口
		// 解析 HTTP 地址
		http, err := config.HTTPAddress("http://" + *httpBindAddr)
		if err != nil {
			logrus.WithError(err).Fatalf("解析 HTTP 地址失败")
		}
		httpAddr = http

		// 解析 HTTPS 地址
		https, err := config.HTTPAddress("https://" + *httpsBindAddr)
		if err != nil {
			logrus.WithError(err).Fatalf("解析 HTTPS 地址失败")
		}
		httpsAddr = https
	} else {
		// 使用 Unix Socket（实验性功能）
		socket, err := config.UnixSocketAddress(*unixSocket, *unixSocketPermission)
		if err != nil {
			logrus.WithError(err).Fatalf("解析 Unix Socket 失败")
		}
		httpAddr = socket
	}
	// 配置验证 - 检查配置文件是否有错误
	configErrors := &config.ConfigErrors{}
	cfg.Verify(configErrors) // 验证配置的有效性
	if len(*configErrors) > 0 {
		// 如果有配置错误，打印所有错误并退出
		for _, err := range *configErrors {
			logrus.Errorf("配置错误: %s", err)
		}
		logrus.Fatalf("由于配置错误，启动失败")
	}

	// 创建进程上下文 - 用于管理组件的生命周期
	processCtx := process.NewProcessContext()

	// 设置日志系统
	internal.SetupStdLogging()             // 设置标准输出日志
	internal.SetupHookLogging(cfg.Logging) // 设置基于配置的日志钩子
	internal.SetupPprof()                  // 设置性能分析工具

	// 平台兼容性检查  不检查
	// basepkg.PlatformSanityChecks()

	// 打印版本信息
	// logrus.Infof("Dendrite 版本 %s", internal.VersionString())

	// 检查是否启用了开放注册（安全警告）
	if !cfg.ClientAPI.RegistrationDisabled && cfg.ClientAPI.OpenRegistrationWithoutVerificationEnabled {
		logrus.Warn("开放注册已启用")
	}

	// 创建 DNS 缓存 - 用于减少 DNS 查询次数，提高性能 // 也就是ip 对应域名 然后直接用 ip 请求
	var dnsCache *fclient.DNSCache
	if cfg.Global.DNSCache.Enabled {
		// 根据配置创建 DNS 缓存
		dnsCache = fclient.NewDNSCache(
			cfg.Global.DNSCache.CacheSize,       // 缓存大小
			cfg.Global.DNSCache.CacheLifetime,   // 缓存生存时间
			cfg.FederationAPI.AllowNetworkCIDRs, // 允许的网络 CIDR 范围
			cfg.FederationAPI.DenyNetworkCIDRs,  // 禁止的网络 CIDR 范围
		)
		logrus.Infof(
			"DNS 缓存已启用 (大小 %d, 生存时间 %s)",
			cfg.Global.DNSCache.CacheSize,
			cfg.Global.DNSCache.CacheLifetime,
		)
	}

	// 设置分布式追踪 - 用于性能监控和调试
	closer, err := cfg.SetupTracing()
	if err != nil {
		logrus.WithError(err).Panicf("启动 OpenTracing 失败")
	}
	defer closer.Close() // 程序结束时关闭追踪器

	// 设置 Sentry 错误监控 - 用于生产环境错误收集
	if cfg.Global.Sentry.Enabled {
		logrus.Info("正在设置 Sentry 调试工具...")
		err = sentry.Init(sentry.ClientOptions{
			Dsn:              cfg.Global.Sentry.DSN,                  // Sentry 数据源名称
			Environment:      cfg.Global.Sentry.Environment,          // 环境标识（如 production, staging）
			Debug:            true,                                   // 启用调试模式
			ServerName:       string(cfg.Global.ServerName),          // 服务器名称
			Release:          "dendrite@" + internal.VersionString(), // 版本信息
			AttachStacktrace: true,                                   // 附加堆栈跟踪
		})
		if err != nil {
			logrus.WithError(err).Panic("启动 Sentry 失败")
		}

		// 启动 Sentry 清理协程 - 确保程序关闭时正确清理 Sentry 事件
		go func() {
			processCtx.ComponentStarted()       // 标记组件已启动
			<-processCtx.WaitForShutdown()      // 等待关闭信号
			if !sentry.Flush(time.Second * 5) { // 刷新所有待发送的事件（5秒超时）
				logrus.Warnf("刷新所有 Sentry 事件失败！")
			}
			processCtx.ComponentFinished() // 标记组件已完成
		}()
	}

	// 创建客户端 - 用于与外部服务通信
	// 首先这个可以关闭 配置文件中
	// 1 配置关闭 2 采用空实现 federationClient := &noOpFederationClient{} // 空实现的联邦客户端
	// 3 修改所有依赖的组件 比较麻烦
	federationClient := basepkg.CreateFederationClient(cfg, dnsCache) // 联邦客户端（与其他 Matrix 服务器通信）
	httpClient := basepkg.CreateClient(cfg, dnsCache)                 // 通用 HTTP 客户端

	// 准备必需的依赖组件
	cm := sqlutil.NewConnectionManager(processCtx, cfg.Global.DatabaseOptions) // 数据库连接管理器
	routers := httputil.NewRouters()                                           // HTTP 路由器集合 吧其他都串起来 如果是gin

	// 创建缓存系统 - 提高性能，减少数据库查询
	caches := caching.NewRistrettoCache(cfg.Global.Cache.EstimatedMaxSize, cfg.Global.Cache.MaxAge, caching.EnableMetrics)

	// 创建 NATS JetStream 实例 - 用于微服务间的消息传递
	natsInstance := jetstream.NATSInstance{}

	// 创建房间服务器 API - 负责房间状态管理和事件处理
	rsAPI := roomserver.NewInternalAPI(processCtx, cfg, cm, &natsInstance, caches, caching.EnableMetrics)

	// 创建联邦 API - 负责与其他 Matrix 服务器的通信
	fsAPI := federationapi.NewInternalAPI(
		processCtx, cfg, cm, &natsInstance, federationClient, rsAPI, caches, nil, false,
	)
	/**
		   fsAPI 是 FederationServer API 的缩写，它是 Dendrite 中负责处理与其他 Matrix 服务器通信的核心组件。让我详细解释：

		   FederationServer API 的实例
		Dendrite 的联邦通信管理器
		负责与其他 Matrix 服务器的所有交互
		实现 Matrix 联邦协议

		// Dendrite 联邦架构
		┌─────────────┐    ┌─────────────┐    ┌─────────────┐
		│   本地用户   │───▶│ ClientAPI   │───▶│   rsAPI     │
		└─────────────┘    └─────────────┘    └─────────────┘
		                                             │
		                                             ▼
		┌─────────────┐    ┌─────────────┐    ┌─────────────┐
		│  远程服务器  │◀──▶│   fsAPI     │◀───│  事件处理   │
		└─────────────┘    │(Federation) │    └─────────────┘
		                   └─────────────┘
		                          │
		                          ▼
		                   ┌─────────────┐
		                   │  密钥管理   │
		                   └─────────────┘
						   // 事件从创建到最终处理的完整生命周期
	创建事件 → 签名事件 → 验证事件 → 授权检查 → 存储事件 → 分发事件 → 同步给用户
	    ↓         ↓         ↓         ↓         ↓         ↓         ↓
	ClientAPI → rsAPI → rsAPI → rsAPI → Database → SyncAPI → 客户端


	*/

	// 获取密钥环 - 用于验证和签名 Matrix 事件
	keyRing := fsAPI.KeyRing()

	// 设置组件间的依赖关系
	// 房间服务器需要能够调用联邦发送器
	// 这与 rsAPI 不同，rsAPI 可以是 HTTP 客户端，不需要这种依赖  RoomServer API 的实例
	// 其他组件也需要在其依赖项启动后进行更新
	rsAPI.SetFederationAPI(fsAPI, keyRing)

	/*
			     // Dendrite 组件依赖关系
			   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
			   │ ClientAPI   │───▶│   rsAPI     │◀───│FederationAPI│
			   └─────────────┘    │(RoomServer) │    └─────────────┘
			                      └─────────────┘
			                             │
			                             ▼
			                      ┌─────────────┐
			                      │  Database   │
			                      │ (房间状态)   │
			                      └─────────────┘

								  sAPI (RoomServer API) 是：

		房间状态的权威来源 - 所有房间状态都由它管理
		事件处理的核心 - 验证、授权、存储所有房间事件
		组件间的协调者 - 连接 ClientAPI、FederationAPI、SyncAPI
		Matrix 协议的实现 - 实现复杂的 Matrix 房间语义
		性能的关键 - 通过缓存和优化确保高性能
		在 Dendrite 中，rsAPI 是最复杂也是最重要的组件，它确保了 Matrix 房间的正确行为和数据一致性。

	*/
	// 创建用户 API - 负责用户账户、设备和密钥管理
	userAPI := userapi.NewInternalAPI(processCtx, cfg, cm, &natsInstance, rsAPI, federationClient, caching.EnableMetrics, fsAPI.IsBlacklistedOrBackingOff)

	// 创建应用服务 API - 负责与外部应用服务的集成
	asAPI := appservice.NewInternalAPI(processCtx, cfg, &natsInstance, userAPI, rsAPI)

	// 创建 AI 服务 API - 负责 AI 对话功能
	aiAPI := aiservice.NewInternalAPI(processCtx, &cfg.AIService, rsAPI, userAPI)

	// 创建红包服务 API - 负责红包功能
	redpacketAPI := redpacketapi.NewInternalAPI(&cfg.RedPacket, cm, rsAPI, userAPI)

	// 设置房间服务器的其他依赖
	rsAPI.SetAppserviceAPI(asAPI) // 设置应用服务 API
	rsAPI.SetUserAPI(userAPI)     // 设置用户 API

	// // 🔥 初始化阅后即焚消息服务（但不启动）
	// if err := roomserver.InitializeExpiringMessages(rsAPI, cfg); err != nil {
	// 	logrus.WithError(err).Error("初始化阅后即焚消息服务失败")
	// 	// 不要因为这个失败就退出，继续启动其他服务
	// }

	// 组装单体架构 - 将所有微服务组件组合成一个单一的应用程序
	monolith := setup.Monolith{
		Config:    cfg,              // 配置信息
		Client:    httpClient,       // HTTP 客户端
		FedClient: federationClient, // 联邦客户端
		KeyRing:   keyRing,          // 密钥环

		AppserviceAPI: asAPI,        // 应用服务 API
		AIServiceAPI:  aiAPI,        // AI 服务 API
		RedPacketAPI:  redpacketAPI, // 红包服务 API
		// 即使在 -http 模式下也要使用具体实现，因为添加公共路由
		// 必须在具体实现上完成，而不是在 HTTP 客户端上，否则联邦 API 会调用自己
		FederationAPI: fsAPI,   // 联邦 API
		RoomserverAPI: rsAPI,   // 房间服务器 API
		UserAPI:       userAPI, // 用户 API
	}

	// 添加所有公共路由 - 设置 Matrix 客户端和联邦 API 端点
	monolith.AddAllPublicRoutes(processCtx, cfg, routers, cm, &natsInstance, caches, caching.EnableMetrics)

	// 启动 AI 服务 - 启动 AI 对话功能的消费者 消息怎么转入到这里呢
	// monolith.StartAIService(processCtx, cfg, &natsInstance)

	// 启用 Matrix 规范变更（MSCs）- 实验性功能
	if len(cfg.MSCs.MSCs) > 0 {
		if err := mscs.Enable(cfg, cm, routers, &monolith, caches); err != nil {
			logrus.WithError(err).Fatalf("启用 MSCs 失败")
		}
	}

	// 设置 Prometheus 监控指标 - 用于监控服务器状态
	upCounter := prometheus.NewCounter(prometheus.CounterOpts{
		Namespace: "dendrite", // 指标命名空间
		Name:      "up",       // 指标名称
		ConstLabels: map[string]string{ // 常量标签
			"version": internal.VersionString(), // 版本信息
		},
	})
	upCounter.Add(1)                   // 增加计数器
	prometheus.MustRegister(upCounter) // 注册到 Prometheus

	// // 🔥 在 HTTP 服务器启动前，启动阅后即焚消息服务
	roomserver.InitializeExpiringMessages(rsAPI, cfg)

	// 直接暴露 Matrix API，而不是放在 /api 路径下
	// 启动 HTTP 服务器（在单独的 goroutine 中运行）
	go func() {
		basepkg.SetupAndServeHTTP(processCtx, cfg, routers, httpAddr, nil, nil)
	}()

	// 如果提供了证书和密钥，则处理 HTTPS
	if *unixSocket == "" && *certFile != "" && *keyFile != "" {
		// 启动 HTTPS 服务器（在单独的 goroutine 中运行）
		go func() {
			basepkg.SetupAndServeHTTP(processCtx, cfg, routers, httpsAddr, certFile, keyFile)
		}()
	}

	fmt.Println("启动时间:", time.Now().Unix()-timeStart.Unix())

	// 阻塞等待，让 HTTP 和 HTTPS 处理器持续服务 API
	// 这会一直运行直到收到关闭信号
	basepkg.WaitForShutdown(processCtx)
}
