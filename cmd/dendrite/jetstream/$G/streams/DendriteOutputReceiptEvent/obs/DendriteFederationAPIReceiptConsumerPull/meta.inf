{"Created": "2025-06-24T12:16:53.989424Z", "Name": "DendriteFederationAPIReceiptConsumerPull", "durable_name": "DendriteFederationAPIReceiptConsumerPull", "name": "DendriteFederationAPIReceiptConsumerPull", "deliver_policy": "all", "ack_policy": "explicit", "ack_wait": 30000000000, "max_deliver": -1, "filter_subject": "DendriteOutputReceiptEvent", "replay_policy": "instant", "max_waiting": 512, "max_ack_pending": 65536, "headers_only": true, "num_replicas": 0, "metadata": {"_nats.req.level": "0"}}