{"Created": "2025-06-24T12:17:11.089892Z", "Name": "DendriteUserAPIReceiptConsumerPull", "durable_name": "DendriteUserAPIReceiptConsumerPull", "name": "DendriteUserAPIReceiptConsumerPull", "deliver_policy": "all", "ack_policy": "explicit", "ack_wait": 30000000000, "max_deliver": -1, "filter_subject": "DendriteOutputReceiptEvent", "replay_policy": "instant", "max_waiting": 512, "max_ack_pending": 65536, "num_replicas": 0, "metadata": {"_nats.req.level": "0"}}