{"Created": "2025-06-24T12:17:28.822431Z", "Name": "DendriteSyncAPIAccountDataConsumerPull", "durable_name": "DendriteSyncAPIAccountDataConsumerPull", "name": "DendriteSyncAPIAccountDataConsumerPull", "deliver_policy": "all", "ack_policy": "explicit", "ack_wait": ***********, "max_deliver": -1, "filter_subject": "DendriteOutputClientData", "replay_policy": "instant", "max_waiting": 512, "max_ack_pending": 65536, "num_replicas": 0, "metadata": {"_nats.req.level": "0"}}